# 扑克语音备忘录 App 原型

## 项目概述

这是一个专为扑克爱好者设计的语音备忘录应用原型，采用HTML、CSS和JavaScript技术栈开发，符合iOS设计规范，针对iPhone 15尺寸优化。

## 功能特性

### 🎯 核心功能
- **语音录制** - 一键录音，支持暂停/恢复
- **备忘录管理** - 分类整理，标签系统
- **音频播放** - 多速播放，进度控制
- **搜索功能** - 快速查找，多维筛选
- **分类管理** - 按扑克游戏类型分类

### 🎨 设计特色
- **扑克主题** - 融入扑克牌、筹码等经典元素
- **iOS风格** - 符合苹果人机界面指南
- **响应式设计** - 适配iPhone 15尺寸 (393×852px)
- **动画效果** - 流畅的过渡和交互动画
- **视觉反馈** - 丰富的视觉和触觉反馈

## 文件结构

```
pkvideo/
├── index.html          # 主入口文件，包含所有页面
├── app.js             # 应用逻辑和交互功能
├── styles.css         # 增强样式和动画效果
├── 需求文档.md         # 详细的需求分析文档
└── README.md          # 项目说明文档
```

## 页面结构

### 1. 主页面 (home-page)
- 应用标题和快速录音按钮
- 功能菜单网格 (我的备忘录、分类管理、搜索、录音)
- 最近记录列表
- 扑克主题装饰元素

### 2. 录音页面 (record-page)
- 大型录音按钮 (筹码样式)
- 实时录音波形动画
- 录音时间显示
- 暂停/停止控制
- 录音设置 (标题、分类)

### 3. 备忘录列表页面 (memos-page)
- 分类筛选标签
- 备忘录卡片列表
- 播放次数和时长信息
- 收藏状态显示

### 4. 播放器页面 (player-page)
- 备忘录信息展示
- 播放进度条
- 播放控制按钮
- 播放速度调节
- 收藏功能

### 5. 分类管理页面 (categories-page)
- 扑克游戏类型列表
- 每个分类的备忘录数量
- 分类图标和颜色

### 6. 搜索页面 (search-page)
- 搜索输入框
- 快速筛选选项
- 搜索结果展示

### 7. 设置页面 (settings-page)
- 录音设置 (音频质量、最大时长)
- 播放设置 (默认速度)
- 数据管理 (导出、清理)
- 应用信息

## 技术实现

### 前端技术栈
- **HTML5** - 页面结构和语义化标记
- **Tailwind CSS** - 快速样式开发和响应式设计
- **Vanilla JavaScript** - 原生JS实现交互逻辑
- **Font Awesome** - 图标库
- **CSS3 动画** - 自定义动画效果

### 核心特性实现
- **页面导航** - 单页应用 (SPA) 架构
- **状态管理** - 全局状态对象管理应用数据
- **本地存储** - 模拟数据持久化
- **响应式设计** - iPhone 15 尺寸适配
- **动画系统** - CSS3 + JavaScript 动画

### 扑克主题元素
- **色彩方案** - 红、黑、金色经典扑克配色
- **图标系统** - 扑克花色图标 (♠️♥️♦️♣️)
- **视觉元素** - 筹码、卡片、装饰图案
- **交互反馈** - 扑克主题的按钮和动画

## 使用说明

### 启动应用
1. 在浏览器中打开 `index.html` 文件
2. 应用会自动加载并显示主页面
3. 可以看到预置的示例数据

### 主要操作流程

#### 录制备忘录
1. 点击主页面的金色录音按钮或进入录音页面
2. 点击红色录音按钮开始录音
3. 可以暂停/恢复录音
4. 设置标题和分类
5. 点击停止按钮完成录音

#### 播放备忘录
1. 在主页面或备忘录列表中点击播放按钮
2. 进入播放器页面
3. 使用播放控制按钮
4. 可以调节播放速度
5. 支持收藏功能

#### 管理备忘录
1. 进入"我的备忘录"页面
2. 使用分类标签筛选
3. 查看备忘录详细信息
4. 支持播放、编辑、删除操作

#### 搜索功能
1. 进入搜索页面
2. 输入关键词搜索
3. 使用快速筛选选项
4. 查看搜索结果

## 设计亮点

### 1. 扑克主题设计
- 使用扑克经典的红黑金配色
- 融入扑克牌花色图标
- 筹码样式的按钮设计
- 卡片式的信息展示

### 2. iOS设计规范
- 符合苹果人机界面指南
- 使用系统字体和标准间距
- 遵循iOS导航模式
- 支持触觉反馈概念

### 3. 动画和交互
- 流畅的页面切换动画
- 录音时的波形动画
- 按钮点击反馈效果
- 卡片悬浮和光泽效果

### 4. 用户体验
- 直观的操作流程
- 清晰的视觉层次
- 友好的错误提示
- 快速的响应时间

## 示例数据

应用包含以下预置示例数据：
- 德州扑克策略分析 (已收藏)
- 奥马哈起手牌选择
- 对手读牌技巧 (已收藏)
- 资金管理心得

## 浏览器兼容性

- Chrome 80+
- Safari 13+
- Firefox 75+
- Edge 80+

## 开发说明

### 本地开发
1. 克隆或下载项目文件
2. 使用本地服务器打开 (推荐)
3. 或直接在浏览器中打开 `index.html`

### 自定义修改
- 修改 `styles.css` 调整视觉样式
- 编辑 `app.js` 添加新功能
- 更新 `index.html` 调整页面结构

### 扩展功能
- 集成真实的录音API
- 添加云端同步功能
- 实现数据导出/导入
- 增加更多扑克游戏类型

## 注意事项

1. **录音功能** - 当前为模拟实现，实际应用需要集成Web Audio API
2. **数据存储** - 使用内存存储，刷新页面会重置数据
3. **响应式** - 主要针对iPhone 15尺寸优化
4. **浏览器权限** - 实际录音需要麦克风权限

## 后续开发建议

1. **功能完善**
   - 集成真实录音功能
   - 实现本地数据持久化
   - 添加数据备份恢复

2. **用户体验优化**
   - 添加更多动画效果
   - 优化加载性能
   - 增强错误处理

3. **功能扩展**
   - 支持音频剪辑
   - 添加标签管理
   - 实现分享功能

## 联系信息

这是一个原型演示项目，展示了扑克语音备忘录应用的设计理念和核心功能。如需进一步开发或有任何问题，请联系开发团队。
