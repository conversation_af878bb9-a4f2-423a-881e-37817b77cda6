// 扑克语音备忘录应用 JavaScript

console.log('app.js开始加载...');

// 全局状态
let isRecording = false;
let isPaused = false;
let recordingStartTime = null;
let recordingTimer = null;
let currentMemos = [];
let selectedCard = null;
let categories = [
    { id: 'texas', name: '德州扑克', icon: 'fas fa-heart', color: 'red' },
    { id: 'omaha', name: '奥马哈', icon: 'fas fa-diamond', color: 'red' },
    { id: 'stud', name: '七张牌', icon: 'fas fa-club', color: 'black' },
    { id: 'other', name: '其他', icon: 'fas fa-spade', color: 'black' }
];

// 扑克牌数据
const playingCards = [
    // 红桃 (Hearts)
    { suit: '♥', value: 'A', color: 'red', name: '红桃A' },
    { suit: '♥', value: '2', color: 'red', name: '红桃2' },
    { suit: '♥', value: '3', color: 'red', name: '红桃3' },
    { suit: '♥', value: '4', color: 'red', name: '红桃4' },
    { suit: '♥', value: '5', color: 'red', name: '红桃5' },
    { suit: '♥', value: '6', color: 'red', name: '红桃6' },
    { suit: '♥', value: '7', color: 'red', name: '红桃7' },
    { suit: '♥', value: '8', color: 'red', name: '红桃8' },
    { suit: '♥', value: '9', color: 'red', name: '红桃9' },
    { suit: '♥', value: '10', color: 'red', name: '红桃10' },
    { suit: '♥', value: 'J', color: 'red', name: '红桃J' },
    { suit: '♥', value: 'Q', color: 'red', name: '红桃Q' },
    { suit: '♥', value: 'K', color: 'red', name: '红桃K' },

    // 方块 (Diamonds)
    { suit: '♦', value: 'A', color: 'red', name: '方块A' },
    { suit: '♦', value: '2', color: 'red', name: '方块2' },
    { suit: '♦', value: '3', color: 'red', name: '方块3' },
    { suit: '♦', value: '4', color: 'red', name: '方块4' },
    { suit: '♦', value: '5', color: 'red', name: '方块5' },
    { suit: '♦', value: '6', color: 'red', name: '方块6' },
    { suit: '♦', value: '7', color: 'red', name: '方块7' },
    { suit: '♦', value: '8', color: 'red', name: '方块8' },
    { suit: '♦', value: '9', color: 'red', name: '方块9' },
    { suit: '♦', value: '10', color: 'red', name: '方块10' },
    { suit: '♦', value: 'J', color: 'red', name: '方块J' },
    { suit: '♦', value: 'Q', color: 'red', name: '方块Q' },
    { suit: '♦', value: 'K', color: 'red', name: '方块K' },

    // 黑桃 (Spades)
    { suit: '♠', value: 'A', color: 'black', name: '黑桃A' },
    { suit: '♠', value: '2', color: 'black', name: '黑桃2' },
    { suit: '♠', value: '3', color: 'black', name: '黑桃3' },
    { suit: '♠', value: '4', color: 'black', name: '黑桃4' },
    { suit: '♠', value: '5', color: 'black', name: '黑桃5' },
    { suit: '♠', value: '6', color: 'black', name: '黑桃6' },
    { suit: '♠', value: '7', color: 'black', name: '黑桃7' },
    { suit: '♠', value: '8', color: 'black', name: '黑桃8' },
    { suit: '♠', value: '9', color: 'black', name: '黑桃9' },
    { suit: '♠', value: '10', color: 'black', name: '黑桃10' },
    { suit: '♠', value: 'J', color: 'black', name: '黑桃J' },
    { suit: '♠', value: 'Q', color: 'black', name: '黑桃Q' },
    { suit: '♠', value: 'K', color: 'black', name: '黑桃K' },

    // 梅花 (Clubs)
    { suit: '♣', value: 'A', color: 'black', name: '梅花A' },
    { suit: '♣', value: '2', color: 'black', name: '梅花2' },
    { suit: '♣', value: '3', color: 'black', name: '梅花3' },
    { suit: '♣', value: '4', color: 'black', name: '梅花4' },
    { suit: '♣', value: '5', color: 'black', name: '梅花5' },
    { suit: '♣', value: '6', color: 'black', name: '梅花6' },
    { suit: '♣', value: '7', color: 'black', name: '梅花7' },
    { suit: '♣', value: '8', color: 'black', name: '梅花8' },
    { suit: '♣', value: '9', color: 'black', name: '梅花9' },
    { suit: '♣', value: '10', color: 'black', name: '梅花10' },
    { suit: '♣', value: 'J', color: 'black', name: '梅花J' },
    { suit: '♣', value: 'Q', color: 'black', name: '梅花Q' },
    { suit: '♣', value: 'K', color: 'black', name: '梅花K' }
];

// 隐藏滚动条的通用函数
function hideScrollbars() {
    // 为所有可能有滚动条的元素添加样式
    const scrollableElements = document.querySelectorAll('*');
    scrollableElements.forEach(element => {
        const computedStyle = window.getComputedStyle(element);
        if (computedStyle.overflowY === 'auto' || computedStyle.overflowY === 'scroll' ||
            computedStyle.overflow === 'auto' || computedStyle.overflow === 'scroll' ||
            element.classList.contains('overflow-y-auto') ||
            element.classList.contains('overflow-auto') ||
            element.classList.contains('overflow-scroll')) {
            element.style.setProperty('scrollbar-width', 'none', 'important');
            element.style.setProperty('-ms-overflow-style', 'none', 'important');
        }
    });
}

// 页面导航
function showPage(pageId) {
    console.log('showPage被调用，目标页面:', pageId);

    try {
        // 隐藏所有页面
        document.querySelectorAll('.page').forEach(page => {
            page.classList.remove('active');
        });

        // 显示目标页面
        const targetPage = document.getElementById(pageId);
        console.log('目标页面元素:', targetPage);

        if (targetPage) {
            targetPage.classList.add('active');
            targetPage.classList.add('slide-in');

            // 移除动画类
            setTimeout(() => {
                targetPage.classList.remove('slide-in');
            }, 300);

            console.log('页面切换成功:', pageId);
        } else {
            console.error('找不到页面元素:', pageId);
        }

        // 切换背景样式
        changeBackgroundForPage(pageId);

        // 隐藏滚动条
        setTimeout(() => {
            hideScrollbars();
        }, 100);

        // 页面特定的初始化
        switch(pageId) {
            case 'memos-page':
                loadMemosList();
                break;
            case 'categories-page':
                loadCategoriesList();
                break;
            case 'search-page':
                focusSearchInput();
                break;
            case 'player-page':
                initializePlayer();
                break;
        }
    } catch (error) {
        console.error('showPage函数执行错误:', error);
    }
}

// 背景切换功能
function changeBackgroundForPage(pageId) {
    const appContainer = document.getElementById('app-container');
    if (!appContainer) return;

    // 移除所有背景类
    appContainer.classList.remove('home-bg', 'record-bg', 'memos-bg', 'player-bg');

    // 根据页面添加对应背景
    switch(pageId) {
        case 'home-page':
            appContainer.classList.add('home-bg');
            createFloatingElements();
            startContinuousFloating();
            break;
        case 'record-page':
            appContainer.classList.add('record-bg');
            clearFloatingElements();
            stopContinuousFloating();
            break;
        case 'memos-page':
            appContainer.classList.add('memos-bg');
            clearFloatingElements();
            stopContinuousFloating();
            break;
        case 'player-page':
            appContainer.classList.add('player-bg');
            clearFloatingElements();
            stopContinuousFloating();
            break;
        default:
            appContainer.classList.add('home-bg');
            createFloatingElements();
            startContinuousFloating();
            break;
    }
}

// 创建飘浮元素
function createFloatingElements() {
    createFloatingCards();
    createFloatingChips();
}

// 创建飘浮扑克牌
function createFloatingCards() {
    const container = document.getElementById('floating-cards');
    if (!container) return;

    // 清空现有元素
    container.innerHTML = '';

    // 创建多张飘浮扑克牌
    const cardCount = 8;
    for (let i = 0; i < cardCount; i++) {
        setTimeout(() => {
            const card = document.createElement('div');
            card.className = 'floating-card';

            // 随机选择扑克牌
            const randomCard = playingCards[Math.floor(Math.random() * playingCards.length)];
            card.style.color = randomCard.color === 'red' ? '#dc2626' : '#1f2937';
            card.innerHTML = `
                <div style="font-size: 6px;">${randomCard.value}</div>
                <div style="font-size: 10px;">${randomCard.suit}</div>
            `;

            // 随机位置
            card.style.left = Math.random() * 100 + '%';
            card.style.animationDelay = Math.random() * 15 + 's';
            card.style.animationDuration = (15 + Math.random() * 10) + 's';

            container.appendChild(card);

            // 卡片动画结束后移除
            card.addEventListener('animationend', () => {
                if (card.parentNode) {
                    card.parentNode.removeChild(card);
                }
            });
        }, i * 2000); // 每2秒创建一张新卡片
    }
}

// 创建飘浮筹码
function createFloatingChips() {
    const container = document.getElementById('floating-chips');
    if (!container) return;

    // 清空现有元素
    container.innerHTML = '';

    // 创建多个飘浮筹码
    const chipCount = 6;
    const colors = ['#fbbf24', '#ef4444', '#10b981', '#3b82f6', '#8b5cf6'];

    for (let i = 0; i < chipCount; i++) {
        setTimeout(() => {
            const chip = document.createElement('div');
            chip.className = 'floating-chip';

            // 随机颜色
            const randomColor = colors[Math.floor(Math.random() * colors.length)];
            chip.style.background = `radial-gradient(circle, ${randomColor} 0%, ${randomColor}dd 50%, ${randomColor}aa 100%)`;

            // 随机位置
            chip.style.left = Math.random() * 100 + '%';
            chip.style.animationDelay = Math.random() * 12 + 's';
            chip.style.animationDuration = (12 + Math.random() * 8) + 's';

            container.appendChild(chip);

            // 筹码动画结束后移除
            chip.addEventListener('animationend', () => {
                if (chip.parentNode) {
                    chip.parentNode.removeChild(chip);
                }
            });
        }, i * 2500); // 每2.5秒创建一个新筹码
    }
}

// 清除飘浮元素
function clearFloatingElements() {
    const cardsContainer = document.getElementById('floating-cards');
    const chipsContainer = document.getElementById('floating-chips');

    if (cardsContainer) {
        cardsContainer.innerHTML = '';
    }
    if (chipsContainer) {
        chipsContainer.innerHTML = '';
    }
}

// 持续创建飘浮元素
let floatingInterval = null;

function startContinuousFloating() {
    // 清除现有的定时器
    if (floatingInterval) {
        clearInterval(floatingInterval);
    }

    // 每30秒创建新的飘浮元素
    floatingInterval = setInterval(() => {
        if (document.getElementById('home-page').classList.contains('active')) {
            createFloatingElements();
        }
    }, 30000);
}

function stopContinuousFloating() {
    if (floatingInterval) {
        clearInterval(floatingInterval);
        floatingInterval = null;
    }
}

// 录音功能
function startQuickRecord() {
    console.log('startQuickRecord被调用');
    showPage('record-page');
    setTimeout(() => {
        toggleRecording();
    }, 300);
}

function toggleRecording() {
    console.log('toggleRecording被调用，当前录音状态:', isRecording);
    if (!isRecording) {
        startRecording();
    } else {
        stopRecording();
    }
}

function startRecording() {
    isRecording = true;
    isPaused = false;
    recordingStartTime = Date.now();

    // 更新UI
    const recordBtn = document.getElementById('record-btn');
    const recordStatus = document.getElementById('record-status');
    const pauseBtn = document.getElementById('pause-btn');
    const stopBtn = document.getElementById('stop-btn');
    const waveContainer = document.getElementById('wave-container');

    recordBtn.classList.add('pulse-enhanced');
    recordBtn.classList.remove('bg-red-500');
    recordBtn.classList.add('bg-red-600');
    recordStatus.textContent = '正在录音...';
    pauseBtn.disabled = false;
    stopBtn.disabled = false;

    // 显示波形动画
    if (waveContainer) {
        waveContainer.style.display = 'flex';
    }

    // 开始计时
    startTimer();

    // 模拟录音开始
    console.log('开始录音');
}

function pauseRecording() {
    if (isRecording && !isPaused) {
        isPaused = true;
        clearInterval(recordingTimer);
        
        const recordStatus = document.getElementById('record-status');
        const pauseBtn = document.getElementById('pause-btn');
        
        recordStatus.textContent = '录音已暂停';
        pauseBtn.innerHTML = '<i class="fas fa-play"></i>';
        pauseBtn.onclick = resumeRecording;
        
        console.log('暂停录音');
    }
}

function resumeRecording() {
    if (isRecording && isPaused) {
        isPaused = false;
        startTimer();
        
        const recordStatus = document.getElementById('record-status');
        const pauseBtn = document.getElementById('pause-btn');
        
        recordStatus.textContent = '正在录音...';
        pauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
        pauseBtn.onclick = pauseRecording;
        
        console.log('恢复录音');
    }
}

function stopRecording() {
    isRecording = false;
    isPaused = false;
    clearInterval(recordingTimer);

    // 更新UI
    const recordBtn = document.getElementById('record-btn');
    const recordStatus = document.getElementById('record-status');
    const recordTime = document.getElementById('record-time');
    const pauseBtn = document.getElementById('pause-btn');
    const stopBtn = document.getElementById('stop-btn');
    const waveContainer = document.getElementById('wave-container');

    recordBtn.classList.remove('pulse-enhanced', 'bg-red-600');
    recordBtn.classList.add('bg-red-500');
    recordStatus.textContent = '录音完成';
    pauseBtn.disabled = true;
    stopBtn.disabled = true;

    // 隐藏波形动画
    if (waveContainer) {
        waveContainer.style.display = 'none';
    }

    // 保存录音
    saveRecording();

    console.log('停止录音');
}

function startTimer() {
    recordingTimer = setInterval(() => {
        if (!isPaused) {
            const elapsed = Date.now() - recordingStartTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            
            const timeDisplay = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('record-time').textContent = timeDisplay;
        }
    }, 1000);
}

function saveRecording() {
    const title = document.getElementById('memo-title').value || '未命名备忘录';
    const category = document.getElementById('memo-category').value;
    const duration = document.getElementById('record-time').textContent;

    const memo = {
        id: Date.now().toString(),
        title: title,
        category: category,
        duration: duration,
        createdAt: new Date(),
        isStarred: false,
        playCount: 0,
        cardLogo: selectedCard || playingCards[0] // 默认使用红桃A
    };

    currentMemos.unshift(memo);
    updateMemoCount();

    // 清空表单
    document.getElementById('memo-title').value = '';
    document.getElementById('record-time').textContent = '00:00';
    selectedCard = null;

    // 显示成功消息
    showToast('录音保存成功！');

    // 返回主页
    setTimeout(() => {
        showPage('home-page');
    }, 1500);
}

// 扑克牌选择器功能
function showCardSelector() {
    console.log('showCardSelector被调用');
    const modal = document.getElementById('card-selector-modal');
    console.log('模态框元素:', modal);
    if (modal) {
        modal.style.display = 'flex';
        renderCardSelector();
    } else {
        console.error('找不到card-selector-modal元素');
    }
}

function hideCardSelector() {
    console.log('hideCardSelector被调用');
    const modal = document.getElementById('card-selector-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

function renderCardSelector() {
    const grid = document.getElementById('card-selector-grid');
    if (!grid) return;

    grid.innerHTML = playingCards.map((card, index) => `
        <div class="playing-card ${selectedCard && selectedCard.name === card.name ? 'selected' : ''}"
             onclick="selectCard(${index})">
            <div class="card-value" style="color: ${card.color === 'red' ? '#dc2626' : '#1f2937'}">
                ${card.value}
            </div>
            <div class="card-suit" style="color: ${card.color === 'red' ? '#dc2626' : '#1f2937'}">
                ${card.suit}
            </div>
            <div class="card-suit-small" style="color: ${card.color === 'red' ? '#dc2626' : '#1f2937'}">
                ${card.suit}
            </div>
        </div>
    `).join('');
}

function selectCard(index) {
    selectedCard = playingCards[index];
    renderCardSelector();

    // 更新选中的卡片显示
    const selectedCardDisplay = document.getElementById('selected-card-display');
    if (selectedCardDisplay) {
        selectedCardDisplay.innerHTML = `
            <div class="mini-card" style="color: ${selectedCard.color === 'red' ? '#dc2626' : '#1f2937'}">
                <div style="font-size: 6px;">${selectedCard.value}</div>
                <div style="font-size: 10px;">${selectedCard.suit}</div>
            </div>
        `;
    }

    showToast(`已选择 ${selectedCard.name}`);
}

// 批量删除状态
let isBatchDeleteMode = false;
let selectedMemos = new Set();

// 备忘录列表
function loadMemosList() {
    const memosList = document.getElementById('memos-list');
    if (!memosList) return;

    if (currentMemos.length === 0) {
        memosList.innerHTML = `
            <div class="text-center py-12">
                <i class="fas fa-microphone text-4xl text-gray-300 mb-4"></i>
                <p class="text-gray-500">还没有备忘录</p>
                <p class="text-gray-400 text-sm">点击录音按钮开始记录</p>
            </div>
        `;
        return;
    }
    
    memosList.innerHTML = currentMemos.map(memo => {
        const category = categories.find(cat => cat.id === memo.category);
        const cardLogo = memo.cardLogo || playingCards[0];
        const isSelected = selectedMemos.has(memo.id);

        return `
            <div class="poker-card p-4 ${isSelected ? 'ring-2 ring-red-500' : ''}"
                 ontouchstart="startLongPress('${memo.id}')"
                 ontouchend="endLongPress()"
                 ontouchmove="endLongPress()">
                <div class="flex items-start justify-between mb-2">
                    <div class="flex items-center flex-1">
                        ${isBatchDeleteMode ? `
                            <div class="mr-3">
                                <input type="checkbox" ${isSelected ? 'checked' : ''}
                                       onchange="toggleMemoSelection('${memo.id}')"
                                       class="w-5 h-5 text-red-600 rounded">
                            </div>
                        ` : ''}
                        <div class="mini-card mr-3" style="color: ${cardLogo.color === 'red' ? '#dc2626' : '#1f2937'}">
                            <div style="font-size: 6px;">${cardLogo.value}</div>
                            <div style="font-size: 10px;">${cardLogo.suit}</div>
                        </div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-800 flex items-center">
                                ${memo.title}
                                ${memo.isStarred ? '<i class="fas fa-star text-yellow-500 ml-2"></i>' : ''}
                            </h3>
                            <p class="text-sm text-gray-500 mt-1">
                                ${formatDate(memo.createdAt)} · ${category ? category.name : '未分类'}
                            </p>
                        </div>
                    </div>
                    ${!isBatchDeleteMode ? `
                        <button class="text-blue-600 p-2" onclick="showMemoOptions('${memo.id}')">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                    ` : ''}
                </div>
                ${!isBatchDeleteMode ? `
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4 text-sm text-gray-500">
                            <span><i class="fas fa-clock mr-1"></i>${memo.duration}</span>
                            <span><i class="fas fa-play mr-1"></i>${memo.playCount}次</span>
                        </div>
                        <button onclick="playMemo('${memo.id}')" class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition-colors">
                            <i class="fas fa-play mr-1"></i>播放
                        </button>
                    </div>
                ` : ''}
            </div>
        `;
    }).join('');

    // 如果是批量删除模式，添加底部操作栏
    if (isBatchDeleteMode) {
        // 创建底部操作栏，使用绝对定位相对于页面容器
        const batchActionsBar = document.createElement('div');
        batchActionsBar.id = 'batch-actions-bar';
        batchActionsBar.className = 'absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 batch-actions-bar z-50';
        batchActionsBar.innerHTML = `
            <div class="flex items-center justify-between">
                <span class="text-gray-600">已选择 ${selectedMemos.size} 项</span>
                <div class="flex space-x-3">
                    <button onclick="cancelBatchDelete()" class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors">
                        取消
                    </button>
                    <button onclick="confirmBatchDelete()"
                            class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors ${selectedMemos.size === 0 ? 'opacity-50 cursor-not-allowed' : ''}"
                            ${selectedMemos.size === 0 ? 'disabled' : ''}>
                        删除 (${selectedMemos.size})
                    </button>
                </div>
            </div>
        `;

        // 将操作栏添加到页面容器中
        const memosPage = document.getElementById('memos-page');
        if (memosPage) {
            // 移除已存在的操作栏
            const existingBar = memosPage.querySelector('#batch-actions-bar');
            if (existingBar) {
                existingBar.remove();
            }
            memosPage.appendChild(batchActionsBar);

            // 调整备忘录列表的底部边距，为操作栏留出空间
            memosList.style.paddingBottom = '80px';
        }
    } else {
        // 移除操作栏并恢复列表样式
        const memosPage = document.getElementById('memos-page');
        if (memosPage) {
            const existingBar = memosPage.querySelector('#batch-actions-bar');
            if (existingBar) {
                existingBar.remove();
            }
        }
        memosList.style.paddingBottom = '0';
    }
}

function playMemo(memoId) {
    const memo = currentMemos.find(m => m.id === memoId);
    if (memo) {
        memo.playCount++;
        memo.lastPlayedAt = Date.now(); // 记录最后播放时间
        showPage('player-page');
        // 这里会在播放器页面中加载具体的备忘录
        loadPlayerWithMemo(memo);
    }
}

function showMemoOptions(memoId) {
    const memo = currentMemos.find(m => m.id === memoId);
    if (!memo) return;

    // 创建选项菜单
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-end justify-center z-50';
    modal.onclick = (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    };

    modal.innerHTML = `
        <div class="bg-white rounded-t-lg w-full max-w-md p-6 animate-slide-up">
            <div class="text-center mb-4">
                <h3 class="text-lg font-semibold text-gray-800">${memo.title}</h3>
                <p class="text-sm text-gray-500">选择操作</p>
            </div>
            <div class="space-y-3">
                <button onclick="playMemo('${memoId}'); this.closest('.fixed').remove();"
                        class="w-full p-3 text-left hover:bg-gray-50 rounded-lg flex items-center">
                    <i class="fas fa-play text-blue-600 mr-3"></i>
                    播放
                </button>
                <button onclick="editMemo('${memoId}'); this.closest('.fixed').remove();"
                        class="w-full p-3 text-left hover:bg-gray-50 rounded-lg flex items-center">
                    <i class="fas fa-edit text-green-600 mr-3"></i>
                    编辑标题
                </button>
                <button onclick="toggleMemoStar('${memoId}'); this.closest('.fixed').remove();"
                        class="w-full p-3 text-left hover:bg-gray-50 rounded-lg flex items-center">
                    <i class="fas fa-star text-yellow-500 mr-3"></i>
                    ${memo.isStarred ? '取消收藏' : '添加收藏'}
                </button>
                <button onclick="shareMemo('${memoId}'); this.closest('.fixed').remove();"
                        class="w-full p-3 text-left hover:bg-gray-50 rounded-lg flex items-center">
                    <i class="fas fa-share text-purple-600 mr-3"></i>
                    分享
                </button>
                <button onclick="deleteMemo('${memoId}'); this.closest('.fixed').remove();"
                        class="w-full p-3 text-left hover:bg-red-50 rounded-lg flex items-center text-red-600">
                    <i class="fas fa-trash mr-3"></i>
                    删除
                </button>
            </div>
            <button onclick="this.closest('.fixed').remove();"
                    class="w-full mt-4 p-3 bg-gray-100 text-gray-700 rounded-lg">
                取消
            </button>
        </div>
    `;

    document.body.appendChild(modal);
}

function editMemo(memoId) {
    const memo = currentMemos.find(m => m.id === memoId);
    if (!memo) return;

    const newTitle = prompt('请输入新的标题:', memo.title);
    if (newTitle && newTitle.trim()) {
        memo.title = newTitle.trim();
        updateMemoCount();
        loadMemosList();
        showToast('标题已更新');
    }
}

function toggleMemoStar(memoId) {
    const memo = currentMemos.find(m => m.id === memoId);
    if (!memo) return;

    memo.isStarred = !memo.isStarred;
    updateMemoCount();
    loadMemosList();
    showToast(memo.isStarred ? '已添加到收藏' : '已取消收藏');
}

function shareMemo(memoId) {
    const memo = currentMemos.find(m => m.id === memoId);
    if (!memo) return;

    // 模拟分享功能
    showToast('分享功能开发中...');
    console.log('分享备忘录:', memo);
}

function deleteMemo(memoId) {
    const memo = currentMemos.find(m => m.id === memoId);
    if (!memo) return;

    // 使用优化的确认对话框
    showDeleteConfirmDialog(memo, () => {
        const index = currentMemos.findIndex(m => m.id === memoId);
        if (index !== -1) {
            currentMemos.splice(index, 1);
            updateMemoCount();
            loadMemosList();
            showToast(`已删除"${memo.title}"`, 'delete');
        }
    });
}

function showDeleteConfirmDialog(memo, onConfirm) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 confirm-dialog';
    modal.onclick = (e) => {
        if (e.target === modal) {
            modal.remove();
            delete window.executeDelete;
        }
    };

    const category = categories.find(cat => cat.id === memo.category);
    const categoryName = category ? category.name : '未分类';
    const cardLogo = memo.cardLogo || playingCards[0];

    modal.innerHTML = `
        <div class="bg-white rounded-xl shadow-2xl p-6 m-4 max-w-sm w-full delete-confirm-enter">
            <div class="text-center mb-6">
                <!-- 动画删除图标 -->
                <div class="w-20 h-20 bg-gradient-to-br from-red-100 to-red-200 rounded-full flex items-center justify-center mx-auto mb-4 relative overflow-hidden">
                    <div class="absolute inset-0 bg-red-500 opacity-10 animate-ping rounded-full"></div>
                    <i class="fas fa-trash text-red-600 text-3xl relative z-10"></i>
                </div>

                <h3 class="text-xl font-bold text-gray-800 mb-4">删除备忘录</h3>

                <!-- 备忘录预览卡片 -->
                <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 mb-4 border border-gray-200">
                    <div class="flex items-center justify-start space-x-3">
                        <!-- 扑克牌logo -->
                        <div class="playing-card" style="width: 32px; height: 44px; flex-shrink: 0;">
                            <div class="card-value" style="color: ${cardLogo.color === 'red' ? '#dc2626' : '#1f2937'}; font-size: 8px;">
                                ${cardLogo.value}
                            </div>
                            <div class="card-suit" style="color: ${cardLogo.color === 'red' ? '#dc2626' : '#1f2937'}; font-size: 12px;">
                                ${cardLogo.suit}
                            </div>
                            <div class="card-suit-small" style="color: ${cardLogo.color === 'red' ? '#dc2626' : '#1f2937'};">
                                ${cardLogo.suit}
                            </div>
                        </div>

                        <!-- 备忘录信息 -->
                        <div class="text-left flex-1 min-w-0">
                            <h4 class="font-semibold text-gray-800 text-sm truncate">${memo.title}</h4>
                            <div class="flex items-center text-xs text-gray-500 mt-1">
                                <span class="truncate">${categoryName}</span>
                                <span class="mx-1">·</span>
                                <span>${memo.duration}</span>
                                ${memo.isStarred ? '<i class="fas fa-star text-yellow-500 ml-1"></i>' : ''}
                            </div>
                            <div class="flex items-center text-xs text-gray-400 mt-1">
                                <i class="fas fa-clock mr-1"></i>
                                <span>${formatDate(memo.createdAt)}</span>
                                <span class="mx-1">·</span>
                                <i class="fas fa-play mr-1"></i>
                                <span>${memo.playCount}次播放</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 确认文字 -->
                <p class="text-gray-600 mb-4 text-sm leading-relaxed">
                    确定要删除这条备忘录吗？<br>
                    <span class="text-gray-500">删除后将无法恢复</span>
                </p>

                <!-- 警告提示 -->
                <div class="bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-lg p-3 mb-2">
                    <div class="flex items-center justify-center">
                        <div class="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
                            <i class="fas fa-exclamation text-white text-xs"></i>
                        </div>
                        <p class="text-sm text-red-700 font-medium">
                            此操作无法撤销
                        </p>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex space-x-3">
                <button onclick="cancelDelete()"
                        class="flex-1 px-4 py-3 text-gray-600 border-2 border-gray-300 rounded-xl hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 font-medium">
                    <i class="fas fa-times mr-2"></i>取消
                </button>
                <button onclick="executeDelete()"
                        class="flex-1 px-4 py-3 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-xl hover:from-red-700 hover:to-red-800 transition-all duration-200 font-medium shadow-lg hover:shadow-xl delete-btn-hover">
                    <i class="fas fa-trash mr-2"></i>确认删除
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 添加键盘事件监听
    const handleKeyPress = (e) => {
        if (e.key === 'Escape') {
            cancelDelete();
        } else if (e.key === 'Enter') {
            executeDelete();
        }
    };
    document.addEventListener('keydown', handleKeyPress);

    // 添加取消删除函数
    window.cancelDelete = () => {
        modal.remove();
        document.removeEventListener('keydown', handleKeyPress);
        delete window.executeDelete;
        delete window.cancelDelete;
    };

    // 添加确认删除函数
    window.executeDelete = () => {
        // 添加删除动画
        const deleteBtn = modal.querySelector('button[onclick="executeDelete()"]');
        deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>删除中...';
        deleteBtn.disabled = true;
        deleteBtn.classList.add('opacity-75');

        // 延迟执行删除，让用户看到反馈
        setTimeout(() => {
            onConfirm();
            modal.remove();
            document.removeEventListener('keydown', handleKeyPress);
            delete window.executeDelete;
            delete window.cancelDelete;
        }, 500);
    };

    // 自动聚焦到取消按钮（更安全的默认选择）
    setTimeout(() => {
        const cancelBtn = modal.querySelector('button[onclick="cancelDelete()"]');
        if (cancelBtn) {
            cancelBtn.focus();
        }
    }, 100);
}

// 批量删除功能
function toggleBatchDelete() {
    isBatchDeleteMode = !isBatchDeleteMode;
    selectedMemos.clear();

    const batchDeleteBtn = document.getElementById('batch-delete-btn');
    if (batchDeleteBtn) {
        if (isBatchDeleteMode) {
            batchDeleteBtn.classList.add('bg-red-600');
            batchDeleteBtn.innerHTML = '<i class="fas fa-times text-xl"></i>';
        } else {
            batchDeleteBtn.classList.remove('bg-red-600');
            batchDeleteBtn.innerHTML = '<i class="fas fa-trash text-xl"></i>';
        }
    }

    loadMemosList();

    if (isBatchDeleteMode) {
        showToast('选择要删除的备忘录', 'info');
    }
}

function toggleMemoSelection(memoId) {
    if (selectedMemos.has(memoId)) {
        selectedMemos.delete(memoId);
    } else {
        selectedMemos.add(memoId);
    }

    // 更新操作栏的计数
    const batchActionsBar = document.getElementById('batch-actions-bar');
    if (batchActionsBar) {
        const countSpan = batchActionsBar.querySelector('span');
        if (countSpan) {
            countSpan.textContent = `已选择 ${selectedMemos.size} 项`;
        }

        // 更新删除按钮状态
        const deleteBtn = batchActionsBar.querySelector('button[onclick="confirmBatchDelete()"]');
        if (deleteBtn) {
            if (selectedMemos.size === 0) {
                deleteBtn.classList.add('opacity-50', 'cursor-not-allowed');
                deleteBtn.disabled = true;
                deleteBtn.innerHTML = '删除 (0)';
            } else {
                deleteBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                deleteBtn.disabled = false;
                deleteBtn.innerHTML = `删除 (${selectedMemos.size})`;
            }
        }
    }

    loadMemosList();
}

function cancelBatchDelete() {
    // 清理操作栏
    const memosPage = document.getElementById('memos-page');
    if (memosPage) {
        const existingBar = memosPage.querySelector('#batch-actions-bar');
        if (existingBar) {
            existingBar.remove();
        }
    }

    // 恢复列表样式
    const memosList = document.getElementById('memos-list');
    if (memosList) {
        memosList.style.paddingBottom = '0';
    }

    toggleBatchDelete();
}

function confirmBatchDelete() {
    if (selectedMemos.size === 0) {
        showToast('请选择要删除的备忘录', 'warning');
        return;
    }

    const count = selectedMemos.size;
    const selectedMemosList = Array.from(selectedMemos).map(id =>
        currentMemos.find(m => m.id === id)
    ).filter(memo => memo);

    // 创建优化的确认对话框
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 confirm-dialog';
    modal.onclick = (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    };

    // 计算总时长
    const totalDuration = selectedMemosList.reduce((total, memo) => {
        const [minutes, seconds] = memo.duration.split(':').map(Number);
        return total + minutes * 60 + seconds;
    }, 0);
    const totalMinutes = Math.floor(totalDuration / 60);
    const totalSeconds = totalDuration % 60;
    const formattedDuration = `${totalMinutes}:${totalSeconds.toString().padStart(2, '0')}`;

    modal.innerHTML = `
        <div class="bg-white rounded-xl shadow-2xl p-6 m-4 max-w-md w-full delete-confirm-enter">
            <div class="text-center mb-6">
                <!-- 批量删除图标 -->
                <div class="w-20 h-20 bg-gradient-to-br from-red-100 to-red-200 rounded-full flex items-center justify-center mx-auto mb-4 relative overflow-hidden">
                    <div class="absolute inset-0 bg-red-500 opacity-10 animate-ping rounded-full"></div>
                    <div class="relative z-10 flex items-center">
                        <i class="fas fa-trash text-red-600 text-2xl mr-1"></i>
                        <span class="bg-red-600 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center">${count}</span>
                    </div>
                </div>

                <h3 class="text-xl font-bold text-gray-800 mb-2">批量删除备忘录</h3>
                <p class="text-gray-600 text-sm mb-4">
                    即将删除 <span class="font-semibold text-red-600">${count}</span> 条备忘录
                </p>

                <!-- 统计信息 -->
                <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 mb-4 border border-gray-200">
                    <div class="grid grid-cols-2 gap-4 text-center">
                        <div>
                            <div class="text-2xl font-bold text-gray-800 stat-number">${count}</div>
                            <div class="text-xs text-gray-500">条备忘录</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-gray-800 stat-number">${formattedDuration}</div>
                            <div class="text-xs text-gray-500">总时长</div>
                        </div>
                    </div>
                </div>

                <!-- 备忘录预览列表 -->
                <div class="max-h-32 overflow-y-auto mb-4" style="scrollbar-width: none; -ms-overflow-style: none;" onload="this.style.setProperty('scrollbar-width', 'none', 'important'); this.style.setProperty('-ms-overflow-style', 'none', 'important');">
                    <div class="space-y-2">
                        ${selectedMemosList.slice(0, 3).map(memo => {
                            const cardLogo = memo.cardLogo || playingCards[0];
                            return `
                                <div class="flex items-center space-x-2 text-left bg-white rounded-lg p-2 border border-gray-100 memo-preview-item">
                                    <div class="mini-card" style="color: ${cardLogo.color === 'red' ? '#dc2626' : '#1f2937'}; width: 16px; height: 22px;">
                                        <div style="font-size: 4px;">${cardLogo.value}</div>
                                        <div style="font-size: 6px;">${cardLogo.suit}</div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="text-xs font-medium text-gray-800 truncate">${memo.title}</div>
                                        <div class="text-xs text-gray-500">${memo.duration}</div>
                                    </div>
                                    ${memo.isStarred ? '<i class="fas fa-star text-yellow-500 text-xs"></i>' : ''}
                                </div>
                            `;
                        }).join('')}
                        ${count > 3 ? `
                            <div class="text-center py-2">
                                <span class="text-xs text-gray-500">还有 ${count - 3} 条备忘录...</span>
                            </div>
                        ` : ''}
                    </div>
                </div>

                <!-- 警告提示 -->
                <div class="bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-lg p-3 mb-2">
                    <div class="flex items-center justify-center">
                        <div class="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center mr-2 flex-shrink-0 warning-pulse">
                            <i class="fas fa-exclamation text-white text-xs"></i>
                        </div>
                        <p class="text-sm text-red-700 font-medium">
                            删除后无法恢复，请谨慎操作
                        </p>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex space-x-3">
                <button onclick="cancelBatchDelete()"
                        class="flex-1 px-4 py-3 text-gray-600 border-2 border-gray-300 rounded-xl hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 font-medium">
                    <i class="fas fa-times mr-2"></i>取消
                </button>
                <button onclick="executeBatchDeleteWithAnimation(${count})"
                        class="flex-1 px-4 py-3 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-xl hover:from-red-700 hover:to-red-800 transition-all duration-200 font-medium shadow-lg hover:shadow-xl delete-btn-hover">
                    <i class="fas fa-trash mr-2"></i>删除全部
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 添加键盘事件监听
    const handleKeyPress = (e) => {
        if (e.key === 'Escape') {
            cancelBatchDelete();
        }
    };
    document.addEventListener('keydown', handleKeyPress);

    // 添加取消删除函数
    window.cancelBatchDelete = () => {
        modal.remove();
        document.removeEventListener('keydown', handleKeyPress);
        delete window.executeBatchDeleteWithAnimation;
        delete window.cancelBatchDelete;
    };

    // 添加带动画的批量删除函数
    window.executeBatchDeleteWithAnimation = (count) => {
        const deleteBtn = modal.querySelector('button[onclick*="executeBatchDeleteWithAnimation"]');
        deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>删除中...';
        deleteBtn.disabled = true;
        deleteBtn.classList.add('opacity-75');

        setTimeout(() => {
            executeBatchDelete(count);
            document.removeEventListener('keydown', handleKeyPress);
            delete window.executeBatchDeleteWithAnimation;
            delete window.cancelBatchDelete;
        }, 800);
    };

    // 自动聚焦到取消按钮
    setTimeout(() => {
        const cancelBtn = modal.querySelector('button[onclick="cancelBatchDelete()"]');
        if (cancelBtn) {
            cancelBtn.focus();
        }
    }, 100);
}

function executeBatchDelete(count) {
    // 删除选中的备忘录
    const selectedIds = Array.from(selectedMemos);
    selectedIds.forEach(id => {
        const index = currentMemos.findIndex(m => m.id === id);
        if (index !== -1) {
            currentMemos.splice(index, 1);
        }
    });

    // 清理操作栏
    const memosPage = document.getElementById('memos-page');
    if (memosPage) {
        const existingBar = memosPage.querySelector('#batch-actions-bar');
        if (existingBar) {
            existingBar.remove();
        }
    }

    // 恢复列表样式
    const memosList = document.getElementById('memos-list');
    if (memosList) {
        memosList.style.paddingBottom = '0';
    }

    // 退出批量删除模式
    isBatchDeleteMode = false;
    selectedMemos.clear();

    const batchDeleteBtn = document.getElementById('batch-delete-btn');
    if (batchDeleteBtn) {
        batchDeleteBtn.classList.remove('bg-red-600');
        batchDeleteBtn.innerHTML = '<i class="fas fa-trash text-xl"></i>';
    }

    updateMemoCount();
    loadMemosList();

    // 关闭对话框
    document.querySelector('.confirm-dialog').remove();

    showToast(`已删除 ${count} 条备忘录`, 'delete');
}

// 长按删除功能
let longPressTimer = null;
let longPressMemoId = null;

function startLongPress(memoId) {
    longPressMemoId = memoId;
    longPressTimer = setTimeout(() => {
        if (longPressMemoId === memoId) {
            // 触发长按删除
            showQuickDeleteDialog(memoId);
        }
    }, 800); // 800ms长按
}

function endLongPress() {
    if (longPressTimer) {
        clearTimeout(longPressTimer);
        longPressTimer = null;
    }
    longPressMemoId = null;
}

function showQuickDeleteDialog(memoId) {
    const memo = currentMemos.find(m => m.id === memoId);
    if (!memo) return;

    // 使用统一的删除确认对话框
    showDeleteConfirmDialog(memo, () => {
        const index = currentMemos.findIndex(m => m.id === memoId);
        if (index !== -1) {
            const deletedMemo = currentMemos[index];
            currentMemos.splice(index, 1);
            updateMemoCount();
            loadMemosList();
            showToast(`已删除"${deletedMemo.title}"`, 'delete');
        }
    });
}







// 工具函数
function formatDate(date) {
    const now = new Date();
    const diff = now - date;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) {
        const hours = Math.floor(diff / (1000 * 60 * 60));
        if (hours === 0) {
            const minutes = Math.floor(diff / (1000 * 60));
            return minutes === 0 ? '刚刚' : `${minutes}分钟前`;
        }
        return `${hours}小时前`;
    } else if (days === 1) {
        return '昨天';
    } else if (days < 7) {
        return `${days}天前`;
    } else {
        return date.toLocaleDateString('zh-CN');
    }
}

function updateMemoCount() {
    const countElement = document.getElementById('memo-count');
    if (countElement) {
        countElement.textContent = `${currentMemos.length} 条记录`;
    }

    // 更新主页面的最近记录
    updateRecentMemos();
}

function updateRecentMemos() {
    const recentMemosContainer = document.getElementById('recent-memos');
    if (!recentMemosContainer) return;

    // 获取最近的3条记录
    const recentMemos = currentMemos.slice(0, 3);

    if (recentMemos.length === 0) {
        recentMemosContainer.innerHTML = `
            <div class="text-center py-8">
                <i class="fas fa-microphone text-3xl text-blue-200 mb-3"></i>
                <p class="text-blue-200">还没有录音记录</p>
                <p class="text-blue-300 text-sm">点击上方按钮开始录音</p>
            </div>
        `;
        return;
    }

    recentMemosContainer.innerHTML = recentMemos.map(memo => {
        const cardLogo = memo.cardLogo || playingCards[0];

        return `
            <div class="poker-card p-3 flex items-center justify-between hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <div class="mini-card mr-3" style="color: ${cardLogo.color === 'red' ? '#dc2626' : '#1f2937'}">
                        <div style="font-size: 6px;">${cardLogo.value}</div>
                        <div style="font-size: 10px;">${cardLogo.suit}</div>
                    </div>
                    <div>
                        <p class="font-medium text-gray-800">${memo.title}</p>
                        <p class="text-xs text-gray-500">${formatDate(memo.createdAt)} · ${memo.duration}</p>
                    </div>
                </div>
                <button onclick="playMemo('${memo.id}')" class="text-blue-600 p-2 hover:bg-blue-50 rounded-lg transition-colors">
                    <i class="fas fa-play"></i>
                </button>
            </div>
        `;
    }).join('');
}

function showToast(message, type = 'info') {
    // 创建toast提示
    const toast = document.createElement('div');

    let bgColor = 'bg-black bg-opacity-80';
    let icon = '';

    if (type === 'success') {
        bgColor = 'bg-green-600';
        icon = '<i class="fas fa-check-circle mr-2"></i>';
    } else if (type === 'error') {
        bgColor = 'bg-red-600';
        icon = '<i class="fas fa-exclamation-circle mr-2"></i>';
    } else if (type === 'warning') {
        bgColor = 'bg-yellow-600';
        icon = '<i class="fas fa-exclamation-triangle mr-2"></i>';
    } else if (type === 'delete') {
        bgColor = 'bg-red-600';
        icon = '<i class="fas fa-trash mr-2"></i>';
    }

    toast.className = `fixed top-20 left-1/2 transform -translate-x-1/2 ${bgColor} text-white px-4 py-3 rounded-lg z-50 shadow-lg flex items-center animate-slide-up`;
    toast.innerHTML = `${icon}${message}`;

    document.body.appendChild(toast);

    // 添加退出动画
    setTimeout(() => {
        toast.style.animation = 'slideUp 0.3s ease-out reverse';
        setTimeout(() => {
            toast.remove();
        }, 300);
    }, 2000);
}

// 分类管理
function loadCategoriesList() {
    const categoriesList = document.getElementById('categories-list');
    if (!categoriesList) return;

    // 计算每个分类的备忘录数量
    const categoryCounts = {};
    categories.forEach(cat => {
        categoryCounts[cat.id] = currentMemos.filter(memo => memo.category === cat.id).length;
    });

    categoriesList.innerHTML = categories.map((category, index) => {
        const cardData = [
            { value: 'A', suit: '♥', color: 'red' },
            { value: 'K', suit: '♦', color: 'red' },
            { value: 'J', suit: '♣', color: 'black' },
            { value: 'Q', suit: '♠', color: 'black' }
        ];
        const card = cardData[index] || cardData[0];

        return `
            <div class="poker-card p-4 flex items-center justify-between card-pattern-bg hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <div class="playing-card mr-4" style="width: 48px; height: 64px;">
                        <div class="card-value" style="color: ${card.color === 'red' ? '#dc2626' : '#1f2937'}; font-size: 12px;">${card.value}</div>
                        <div class="card-suit" style="color: ${card.color === 'red' ? '#dc2626' : '#1f2937'}; font-size: 20px;">${card.suit}</div>
                        <div class="card-suit-small" style="color: ${card.color === 'red' ? '#dc2626' : '#1f2937'};">${card.suit}</div>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-800">${category.name}</h3>
                        <p class="text-sm text-gray-500">${categoryCounts[category.id] || 0} 条备忘录</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <button onclick="editCategory('${category.id}')" class="text-blue-600 p-2 hover:bg-blue-50 rounded-lg transition-colors">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="filterByCategory('${category.id}')" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        `;
    }).join('');
}

function addNewCategory() {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.onclick = (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    };

    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 m-4 max-w-sm w-full">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">添加新分类</h3>
                <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm text-gray-600 mb-1">分类名称</label>
                    <input type="text" id="new-category-name" placeholder="输入分类名称"
                           class="w-full p-3 border border-gray-300 rounded-lg">
                </div>
                <div>
                    <label class="block text-sm text-gray-600 mb-1">选择图标</label>
                    <div class="grid grid-cols-4 gap-2">
                        <button type="button" class="category-icon-btn p-2 border border-gray-300 rounded-lg hover:border-blue-500" data-icon="fas fa-heart" data-color="red">
                            <i class="fas fa-heart text-red-500"></i>
                        </button>
                        <button type="button" class="category-icon-btn p-2 border border-gray-300 rounded-lg hover:border-blue-500" data-icon="fas fa-diamond" data-color="red">
                            <i class="fas fa-diamond text-red-500"></i>
                        </button>
                        <button type="button" class="category-icon-btn p-2 border border-gray-300 rounded-lg hover:border-blue-500" data-icon="fas fa-spade" data-color="black">
                            <i class="fas fa-spade text-gray-800"></i>
                        </button>
                        <button type="button" class="category-icon-btn p-2 border border-gray-300 rounded-lg hover:border-blue-500" data-icon="fas fa-club" data-color="black">
                            <i class="fas fa-club text-gray-800"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-6">
                <button onclick="this.closest('.fixed').remove()"
                        class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors">
                    取消
                </button>
                <button onclick="saveNewCategory()"
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    保存
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 添加图标选择事件
    modal.querySelectorAll('.category-icon-btn').forEach(btn => {
        btn.onclick = () => {
            modal.querySelectorAll('.category-icon-btn').forEach(b => b.classList.remove('border-blue-500', 'bg-blue-50'));
            btn.classList.add('border-blue-500', 'bg-blue-50');
        };
    });

    // 默认选择第一个图标
    modal.querySelector('.category-icon-btn').click();
}

function saveNewCategory() {
    const nameInput = document.getElementById('new-category-name');
    const selectedIcon = document.querySelector('.category-icon-btn.border-blue-500');

    if (!nameInput.value.trim()) {
        showToast('请输入分类名称');
        return;
    }

    if (!selectedIcon) {
        showToast('请选择图标');
        return;
    }

    const newCategory = {
        id: 'custom_' + Date.now(),
        name: nameInput.value.trim(),
        icon: selectedIcon.dataset.icon,
        color: selectedIcon.dataset.color,
        isDefault: false
    };

    categories.push(newCategory);
    loadCategoriesList();
    document.querySelector('.fixed').remove();
    showToast('分类添加成功');
}

function editCategory(categoryId) {
    const category = categories.find(cat => cat.id === categoryId);
    if (!category) return;

    if (category.isDefault !== false) {
        showToast('默认分类不能编辑');
        return;
    }

    const newName = prompt('请输入新的分类名称:', category.name);
    if (newName && newName.trim()) {
        category.name = newName.trim();
        loadCategoriesList();
        showToast('分类名称已更新');
    }
}

function filterByCategory(categoryId) {
    // 跳转到备忘录列表页面并按分类筛选
    showPage('memos-page');

    // 这里可以添加筛选逻辑
    setTimeout(() => {
        const category = categories.find(cat => cat.id === categoryId);
        if (category) {
            showToast(`显示 ${category.name} 分类的备忘录`);
        }
    }, 300);
}

// 搜索功能
let currentSearchQuery = '';
let currentFilters = {
    starred: false,
    recent: false,
    today: false,
    week: false,
    category: null
};

function focusSearchInput() {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.focus();
    }

    // 初始化分类筛选按钮
    initializeCategoryFilters();

    // 清空之前的搜索结果
    resetSearchResults();
}

function initializeCategoryFilters() {
    const categoryFilters = document.getElementById('category-filters');
    if (!categoryFilters) return;

    categoryFilters.innerHTML = categories.map(category => `
        <button onclick="filterByCategory('${category.id}')"
                class="px-3 py-1 bg-blue-100 text-blue-600 rounded-full text-xs hover:bg-blue-200 transition-colors"
                id="category-filter-${category.id}">
            ${category.name}
        </button>
    `).join('');
}

function performSearch() {
    const searchInput = document.getElementById('search-input');
    const clearBtn = document.getElementById('clear-search');

    if (!searchInput) return;

    currentSearchQuery = searchInput.value.trim();

    // 显示/隐藏清除按钮
    if (currentSearchQuery) {
        clearBtn.classList.remove('hidden');
    } else {
        clearBtn.classList.add('hidden');
    }

    // 执行搜索
    executeSearch();
}

function executeSearch() {
    let results = [...currentMemos];

    // 文本搜索
    if (currentSearchQuery) {
        const searchTitle = document.getElementById('search-title').checked;
        const searchCategory = document.getElementById('search-category').checked;

        results = results.filter(memo => {
            let matches = false;

            if (searchTitle && memo.title.toLowerCase().includes(currentSearchQuery.toLowerCase())) {
                matches = true;
            }

            if (searchCategory) {
                const category = categories.find(cat => cat.id === memo.category);
                if (category && category.name.toLowerCase().includes(currentSearchQuery.toLowerCase())) {
                    matches = true;
                }
            }

            return matches;
        });
    }

    // 应用筛选器
    results = applyFilters(results);

    // 显示结果
    displaySearchResults(results);
}

function applyFilters(memos) {
    let filtered = [...memos];

    // 收藏筛选
    if (currentFilters.starred) {
        filtered = filtered.filter(memo => memo.isStarred);
    }

    // 最近播放筛选
    if (currentFilters.recent) {
        filtered = filtered.filter(memo => memo.playCount > 0)
                          .sort((a, b) => (b.lastPlayedAt || 0) - (a.lastPlayedAt || 0));
    }

    // 今天筛选
    if (currentFilters.today) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        filtered = filtered.filter(memo => {
            const memoDate = new Date(memo.createdAt);
            memoDate.setHours(0, 0, 0, 0);
            return memoDate.getTime() === today.getTime();
        });
    }

    // 本周筛选
    if (currentFilters.week) {
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        filtered = filtered.filter(memo => new Date(memo.createdAt) >= weekAgo);
    }

    // 分类筛选
    if (currentFilters.category) {
        filtered = filtered.filter(memo => memo.category === currentFilters.category);
    }

    // 仅收藏筛选
    const searchStarred = document.getElementById('search-starred');
    if (searchStarred && searchStarred.checked) {
        filtered = filtered.filter(memo => memo.isStarred);
    }

    return filtered;
}

function displaySearchResults(results) {
    const searchResults = document.getElementById('search-results');
    const searchHeader = document.getElementById('search-header');
    const noResults = document.getElementById('no-results');
    const resultsCount = document.getElementById('results-count');

    if (!searchResults) return;

    // 隐藏无结果状态
    noResults.classList.add('hidden');

    if (results.length === 0 && (currentSearchQuery || hasActiveFilters())) {
        // 显示无结果状态
        searchResults.innerHTML = '';
        searchHeader.classList.add('hidden');
        noResults.classList.remove('hidden');
        return;
    }

    if (results.length === 0) {
        // 显示初始状态
        searchResults.innerHTML = `
            <div class="text-center py-12">
                <i class="fas fa-search text-4xl text-white opacity-30 mb-4"></i>
                <p class="text-blue-200">输入关键词开始搜索</p>
                <p class="text-blue-300 text-sm mt-2">或使用上方的快速筛选</p>
            </div>
        `;
        searchHeader.classList.add('hidden');
        return;
    }

    // 显示搜索结果
    searchHeader.classList.remove('hidden');
    resultsCount.textContent = `找到 ${results.length} 条结果`;

    searchResults.innerHTML = `
        <div class="space-y-3">
            ${results.map((memo, index) => {
                const category = categories.find(cat => cat.id === memo.category);
                const cardLogo = memo.cardLogo || playingCards[0];

                return `
                    <div class="poker-card p-4 hover:shadow-lg transition-shadow search-result-item">
                        <div class="flex items-start justify-between mb-2">
                            <div class="flex items-center flex-1">
                                <div class="mini-card mr-3" style="color: ${cardLogo.color === 'red' ? '#dc2626' : '#1f2937'}">
                                    <div style="font-size: 6px;">${cardLogo.value}</div>
                                    <div style="font-size: 10px;">${cardLogo.suit}</div>
                                </div>
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-800 flex items-center">
                                        ${highlightSearchTerm(memo.title, currentSearchQuery)}
                                        ${memo.isStarred ? '<i class="fas fa-star text-yellow-500 ml-2"></i>' : ''}
                                    </h3>
                                    <p class="text-sm text-gray-500 mt-1">
                                        ${formatDate(memo.createdAt)} · ${highlightSearchTerm(category ? category.name : '未分类', currentSearchQuery)}
                                        ${memo.lastPlayedAt ? ` · 最后播放: ${formatDate(new Date(memo.lastPlayedAt))}` : ''}
                                    </p>
                                </div>
                            </div>
                            <button class="text-blue-600 p-2" onclick="showMemoOptions('${memo.id}')">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span><i class="fas fa-clock mr-1"></i>${memo.duration}</span>
                                <span><i class="fas fa-play mr-1"></i>${memo.playCount}次</span>
                                ${memo.playCount > 0 ? `<span class="text-green-600"><i class="fas fa-headphones mr-1"></i>已播放</span>` : ''}
                            </div>
                            <button onclick="playMemo('${memo.id}')" class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition-colors">
                                <i class="fas fa-play mr-1"></i>播放
                            </button>
                        </div>
                    </div>
                `;
            }).join('')}
        </div>
    `;

    // 添加结果计数动画
    resultsCount.classList.add('results-counter');
}

function highlightSearchTerm(text, searchTerm) {
    if (!searchTerm) return text;

    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, '<span class="search-highlight">$1</span>');
}

function hasActiveFilters() {
    return currentFilters.starred ||
           currentFilters.recent ||
           currentFilters.today ||
           currentFilters.week ||
           currentFilters.category ||
           document.getElementById('search-starred').checked;
}

function quickFilter(type) {
    // 重置所有筛选器
    Object.keys(currentFilters).forEach(key => {
        if (key !== 'category') {
            currentFilters[key] = false;
        }
    });

    // 移除所有按钮的激活状态
    document.querySelectorAll('[id^="filter-"]').forEach(btn => {
        btn.classList.remove('filter-active');
    });

    // 设置当前筛选器
    currentFilters[type] = true;

    // 添加激活状态
    const filterBtn = document.getElementById(`filter-${type}`);
    if (filterBtn) {
        filterBtn.classList.add('filter-active');
    }

    // 执行搜索
    executeSearch();

    showToast(`已筛选${getFilterName(type)}`, 'info');
}

function filterByCategory(categoryId) {
    // 重置其他筛选器
    Object.keys(currentFilters).forEach(key => {
        if (key !== 'category') {
            currentFilters[key] = false;
        }
    });

    // 移除快速筛选按钮的激活状态
    document.querySelectorAll('[id^="filter-"]').forEach(btn => {
        btn.classList.remove('filter-active');
    });

    // 移除分类按钮的激活状态
    document.querySelectorAll('[id^="category-filter-"]').forEach(btn => {
        btn.classList.remove('bg-blue-500', 'text-white');
        btn.classList.add('bg-blue-100', 'text-blue-600');
    });

    // 设置分类筛选
    currentFilters.category = categoryId;

    // 添加激活状态
    const categoryBtn = document.getElementById(`category-filter-${categoryId}`);
    if (categoryBtn) {
        categoryBtn.classList.remove('bg-blue-100', 'text-blue-600');
        categoryBtn.classList.add('bg-blue-500', 'text-white');
    }

    // 执行搜索
    executeSearch();

    const category = categories.find(cat => cat.id === categoryId);
    showToast(`已筛选${category ? category.name : '未知分类'}`, 'info');
}

function getFilterName(type) {
    const names = {
        starred: '收藏的备忘录',
        recent: '最近播放',
        today: '今天创建',
        week: '本周创建'
    };
    return names[type] || type;
}

function clearSearch() {
    const searchInput = document.getElementById('search-input');
    const clearBtn = document.getElementById('clear-search');

    if (searchInput) {
        searchInput.value = '';
        currentSearchQuery = '';
    }

    if (clearBtn) {
        clearBtn.classList.add('hidden');
    }

    executeSearch();
}

function clearAllFilters() {
    // 重置所有筛选器
    Object.keys(currentFilters).forEach(key => {
        currentFilters[key] = false;
    });
    currentFilters.category = null;

    // 清空搜索框
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.value = '';
        currentSearchQuery = '';
    }

    // 重置搜索选项
    document.getElementById('search-title').checked = true;
    document.getElementById('search-category').checked = false;
    document.getElementById('search-starred').checked = false;

    // 移除所有按钮的激活状态
    document.querySelectorAll('[id^="filter-"]').forEach(btn => {
        btn.classList.remove('filter-active');
    });

    document.querySelectorAll('[id^="category-filter-"]').forEach(btn => {
        btn.classList.remove('bg-blue-500', 'text-white');
        btn.classList.add('bg-blue-100', 'text-blue-600');
    });

    // 隐藏清除按钮
    const clearBtn = document.getElementById('clear-search');
    if (clearBtn) {
        clearBtn.classList.add('hidden');
    }

    // 重置搜索结果
    resetSearchResults();

    showToast('已清除所有筛选条件', 'info');
}

function resetSearchResults() {
    const searchResults = document.getElementById('search-results');
    const searchHeader = document.getElementById('search-header');
    const noResults = document.getElementById('no-results');

    if (searchHeader) {
        searchHeader.classList.add('hidden');
    }

    if (noResults) {
        noResults.classList.add('hidden');
    }

    if (searchResults) {
        searchResults.innerHTML = `
            <div class="text-center py-12">
                <i class="fas fa-search text-4xl text-white opacity-30 mb-4"></i>
                <p class="text-blue-200">输入关键词开始搜索</p>
                <p class="text-blue-300 text-sm mt-2">或使用上方的快速筛选</p>
            </div>
        `;
    }
}

function handleSearchKeyPress(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        performSearch();
        hideSearchSuggestions();
    } else if (event.key === 'Escape') {
        hideSearchSuggestions();
    }
}

// 搜索建议功能
function showSearchSuggestions() {
    const searchInput = document.getElementById('search-input');
    const suggestions = document.getElementById('search-suggestions');

    if (!searchInput || !suggestions) return;

    const query = searchInput.value.trim().toLowerCase();

    if (query.length === 0) {
        // 显示最近搜索或热门搜索
        showRecentSearches();
    } else {
        // 显示匹配的建议
        showMatchingSuggestions(query);
    }

    suggestions.classList.remove('hidden');
}

function hideSearchSuggestions() {
    setTimeout(() => {
        const suggestions = document.getElementById('search-suggestions');
        if (suggestions) {
            suggestions.classList.add('hidden');
        }
    }, 200); // 延迟隐藏，允许点击建议项
}

function showRecentSearches() {
    const suggestions = document.getElementById('search-suggestions');
    if (!suggestions) return;

    // 获取所有备忘录标题作为建议
    const allTitles = currentMemos.map(memo => memo.title).slice(0, 5);

    suggestions.innerHTML = `
        <div class="p-2">
            <div class="text-xs text-gray-500 mb-2 px-2">最近的备忘录</div>
            ${allTitles.map(title => `
                <div onclick="selectSuggestion('${title}')"
                     class="px-3 py-2 hover:bg-gray-100 cursor-pointer rounded text-sm flex items-center">
                    <i class="fas fa-clock text-gray-400 mr-2"></i>
                    ${title}
                </div>
            `).join('')}
        </div>
    `;
}

function showMatchingSuggestions(query) {
    const suggestions = document.getElementById('search-suggestions');
    if (!suggestions) return;

    // 查找匹配的备忘录标题
    const matchingTitles = currentMemos
        .filter(memo => memo.title.toLowerCase().includes(query))
        .map(memo => memo.title)
        .slice(0, 5);

    // 查找匹配的分类
    const matchingCategories = categories
        .filter(cat => cat.name.toLowerCase().includes(query))
        .map(cat => cat.name)
        .slice(0, 3);

    if (matchingTitles.length === 0 && matchingCategories.length === 0) {
        suggestions.innerHTML = `
            <div class="p-4 text-center text-gray-500 text-sm">
                <i class="fas fa-search-minus mb-2"></i>
                <div>没有找到匹配的建议</div>
            </div>
        `;
        return;
    }

    suggestions.innerHTML = `
        <div class="p-2">
            ${matchingTitles.length > 0 ? `
                <div class="text-xs text-gray-500 mb-2 px-2">备忘录</div>
                ${matchingTitles.map(title => `
                    <div onclick="selectSuggestion('${title}')"
                         class="px-3 py-2 hover:bg-gray-100 cursor-pointer rounded text-sm flex items-center">
                        <i class="fas fa-file-audio text-blue-500 mr-2"></i>
                        ${highlightSearchTerm(title, query)}
                    </div>
                `).join('')}
            ` : ''}

            ${matchingCategories.length > 0 ? `
                <div class="text-xs text-gray-500 mb-2 px-2 ${matchingTitles.length > 0 ? 'mt-3' : ''}">分类</div>
                ${matchingCategories.map(categoryName => `
                    <div onclick="selectCategorySuggestion('${categoryName}')"
                         class="px-3 py-2 hover:bg-gray-100 cursor-pointer rounded text-sm flex items-center">
                        <i class="fas fa-tag text-green-500 mr-2"></i>
                        ${highlightSearchTerm(categoryName, query)}
                    </div>
                `).join('')}
            ` : ''}
        </div>
    `;
}

function selectSuggestion(title) {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.value = title;
        performSearch();
    }
    hideSearchSuggestions();
}

function selectCategorySuggestion(categoryName) {
    const category = categories.find(cat => cat.name === categoryName);
    if (category) {
        filterByCategory(category.id);
    }
    hideSearchSuggestions();
}

// 播放器
let isPlaying = false;
let currentPlaybackSpeed = 1.0;
let playbackSpeeds = [0.5, 1.0, 1.5, 2.0];
let currentSpeedIndex = 1;
let currentMemo = null;

function initializePlayer() {
    console.log('初始化播放器');
}

function loadPlayerWithMemo(memo) {
    currentMemo = memo;

    // 更新播放器界面
    document.getElementById('player-title').textContent = memo.title;

    const category = categories.find(cat => cat.id === memo.category);
    const categoryName = category ? category.name : '未分类';
    document.getElementById('player-info').textContent = `${categoryName} · ${formatDate(memo.createdAt)}`;

    document.getElementById('total-time').textContent = memo.duration;
    document.getElementById('current-time').textContent = '0:00';
    document.getElementById('progress-bar').style.width = '0%';

    // 更新扑克牌logo
    const cardLogo = memo.cardLogo || playingCards[0]; // 默认红桃A
    const playerCardLogo = document.getElementById('player-card-logo');
    if (playerCardLogo) {
        playerCardLogo.innerHTML = `
            <div class="playing-card" style="width: 80px; height: 112px;">
                <div class="card-value" style="color: ${cardLogo.color === 'red' ? '#dc2626' : '#1f2937'}; font-size: 16px;">
                    ${cardLogo.value}
                </div>
                <div class="card-suit" style="color: ${cardLogo.color === 'red' ? '#dc2626' : '#1f2937'}; font-size: 32px;">
                    ${cardLogo.suit}
                </div>
                <div class="card-suit-small" style="color: ${cardLogo.color === 'red' ? '#dc2626' : '#1f2937'};">
                    ${cardLogo.suit}
                </div>
            </div>
        `;
    }

    // 更新收藏状态
    const starIcon = document.getElementById('star-icon');
    starIcon.className = memo.isStarred ? 'fas fa-star' : 'far fa-star';

    console.log('加载播放器，播放备忘录:', memo);
}

function togglePlayback() {
    const playPauseBtn = document.getElementById('play-pause-btn');
    const icon = playPauseBtn.querySelector('i');
    const playerCardLogo = document.getElementById('player-card-logo');

    if (!isPlaying) {
        // 开始播放
        isPlaying = true;
        icon.className = 'fas fa-pause';

        // 添加旋转动画
        if (playerCardLogo) {
            const card = playerCardLogo.querySelector('.playing-card');
            if (card) {
                card.classList.add('card-rotate');
            }
        }

        startPlaybackSimulation();
        console.log('开始播放');
    } else {
        // 暂停播放
        isPlaying = false;
        icon.className = 'fas fa-play';

        // 移除旋转动画
        if (playerCardLogo) {
            const card = playerCardLogo.querySelector('.playing-card');
            if (card) {
                card.classList.remove('card-rotate');
            }
        }

        console.log('暂停播放');
    }
}

function startPlaybackSimulation() {
    // 模拟播放进度
    if (!isPlaying) return;

    const progressBar = document.getElementById('progress-bar');
    const currentTimeElement = document.getElementById('current-time');

    // 简单的进度模拟
    let progress = 0;
    const interval = setInterval(() => {
        if (!isPlaying) {
            clearInterval(interval);
            return;
        }

        progress += 1;
        const percentage = Math.min(progress, 100);
        progressBar.style.width = percentage + '%';

        // 更新时间显示
        const totalSeconds = 154; // 2:34 = 154秒
        const currentSeconds = Math.floor((percentage / 100) * totalSeconds);
        const minutes = Math.floor(currentSeconds / 60);
        const seconds = currentSeconds % 60;
        currentTimeElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

        if (progress >= 100) {
            // 播放完成
            isPlaying = false;
            document.getElementById('play-pause-btn').querySelector('i').className = 'fas fa-play';

            // 停止旋转动画
            const playerCardLogo = document.getElementById('player-card-logo');
            if (playerCardLogo) {
                const card = playerCardLogo.querySelector('.playing-card');
                if (card) {
                    card.classList.remove('card-rotate');
                }
            }

            clearInterval(interval);
        }
    }, 100);
}

function changePlaybackSpeed() {
    currentSpeedIndex = (currentSpeedIndex + 1) % playbackSpeeds.length;
    currentPlaybackSpeed = playbackSpeeds[currentSpeedIndex];

    document.getElementById('speed-text').textContent = currentPlaybackSpeed + 'x';
    console.log('播放速度改为:', currentPlaybackSpeed);
}

function toggleStar() {
    if (currentMemo) {
        currentMemo.isStarred = !currentMemo.isStarred;

        const starIcon = document.getElementById('star-icon');
        starIcon.className = currentMemo.isStarred ? 'fas fa-star' : 'far fa-star';

        // 更新备忘录列表中的数据
        const memoIndex = currentMemos.findIndex(m => m.id === currentMemo.id);
        if (memoIndex !== -1) {
            currentMemos[memoIndex] = currentMemo;
        }

        showToast(currentMemo.isStarred ? '已添加到收藏' : '已取消收藏');
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    console.log('扑克语音备忘录应用已加载');

    // 初始化默认选中的扑克牌
    selectedCard = playingCards[0]; // 默认选择红桃A

    // 初始化主页面背景和飘浮元素
    changeBackgroundForPage('home-page');

    // 测试函数是否可用
    console.log('showPage函数:', typeof showPage);
    console.log('toggleRecording函数:', typeof toggleRecording);

    updateMemoCount();

    // 隐藏所有滚动条
    setTimeout(() => {
        hideScrollbars();
    }, 500);

    // 监听DOM变化，确保新添加的元素也隐藏滚动条
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                setTimeout(() => {
                    hideScrollbars();
                }, 50);
            }
        });
    });

    // 开始观察
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // 添加一些示例数据
    if (currentMemos.length === 0) {
        const sampleMemos = [
            {
                id: 'sample1',
                title: '德州扑克策略分析',
                category: 'texas',
                duration: '2:34',
                createdAt: new Date(Date.now() - 2 * 60 * 1000), // 2分钟前
                isStarred: true,
                playCount: 5,
                lastPlayedAt: Date.now() - 10 * 60 * 1000, // 10分钟前播放
                cardLogo: playingCards[0] // 红桃A
            },
            {
                id: 'sample2',
                title: '奥马哈起手牌选择',
                category: 'omaha',
                duration: '1:45',
                createdAt: new Date(Date.now() - 30 * 60 * 1000), // 30分钟前
                isStarred: false,
                playCount: 2,
                lastPlayedAt: Date.now() - 60 * 60 * 1000, // 1小时前播放
                cardLogo: playingCards[25] // 方块K
            },
            {
                id: 'sample3',
                title: '对手读牌技巧',
                category: 'texas',
                duration: '3:12',
                createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2小时前
                isStarred: true,
                playCount: 8,
                lastPlayedAt: Date.now() - 5 * 60 * 1000, // 5分钟前播放
                cardLogo: playingCards[39] // 黑桃A
            },
            {
                id: 'sample4',
                title: '资金管理心得',
                category: 'other',
                duration: '4:56',
                createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1天前
                isStarred: false,
                playCount: 1,
                lastPlayedAt: Date.now() - 12 * 60 * 60 * 1000, // 12小时前播放
                cardLogo: playingCards[51] // 梅花K
            },
            {
                id: 'sample5',
                title: '扑克心理学',
                category: 'other',
                duration: '2:18',
                createdAt: new Date(), // 今天
                isStarred: false,
                playCount: 0,
                cardLogo: playingCards[12] // 红桃K
            }
        ];

        currentMemos.push(...sampleMemos);
        updateMemoCount();
    }
});
