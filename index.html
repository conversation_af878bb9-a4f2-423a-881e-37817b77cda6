<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扑克语音备忘录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        /* iPhone 15 尺寸约束 */
        .iphone-container {
            width: 393px;
            height: 852px;
            margin: 20px auto;
            border: 8px solid #1f2937;
            border-radius: 40px;
            overflow: hidden;
            background: #000;
            position: relative;
        }
        
        .app-container {
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        /* 主页面背景 */
        .app-container.home-bg {
            background:
                linear-gradient(135deg, rgba(30, 58, 138, 0.95) 0%, rgba(124, 45, 18, 0.95) 100%),
                url('https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');
            background-size: cover;
            background-position: center;
        }

        /* 录音页面背景 */
        .app-container.record-bg {
            background:
                linear-gradient(135deg, rgba(30, 58, 138, 0.9) 0%, rgba(124, 45, 18, 0.9) 100%),
                url('https://images.unsplash.com/photo-1606092195730-5d7b9af1efc5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');
            background-size: cover;
            background-position: center;
        }

        /* 备忘录列表背景 */
        .app-container.memos-bg {
            background:
                linear-gradient(135deg, rgba(30, 58, 138, 0.9) 0%, rgba(124, 45, 18, 0.9) 100%),
                url('https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');
            background-size: cover;
            background-position: center;
        }

        /* 播放器背景 */
        .app-container.player-bg {
            background:
                linear-gradient(135deg, rgba(22, 163, 74, 0.9) 0%, rgba(21, 128, 61, 0.9) 100%),
                url('https://images.unsplash.com/photo-1596838132731-3301c3fd4317?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');
            background-size: cover;
            background-position: center;
        }

        .app-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.05) 2px, transparent 2px),
                radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 2px, transparent 2px);
            background-size: 60px 60px;
            pointer-events: none;
            z-index: 1;
        }
        
        /* 扑克主题样式 */
        .poker-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .poker-chip {
            background: radial-gradient(circle, #fbbf24 0%, #f59e0b 50%, #d97706 100%);
            border: 3px solid #ffffff;
            border-radius: 50%;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .recording-pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .card-suit-red { color: #dc2626; }
        .card-suit-black { color: #1f2937; }
        
        /* 状态栏 */
        .status-bar {
            height: 44px;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        
        /* 底部安全区域 */
        .safe-area-bottom {
            height: 34px;
            background: transparent;
        }
        
        /* 隐藏页面 */
        .page {
            display: none;
            height: calc(100% - 78px);
            overflow-y: auto;
        }
        
        .page.active {
            display: block;
        }
        
        /* 导航动画 */
        .slide-in {
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); }
            to { transform: translateX(0); }
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="iphone-container">
        <div class="app-container home-bg" id="app-container">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div>9:41</div>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-xs"></i>
                    <i class="fas fa-wifi text-xs"></i>
                    <i class="fas fa-battery-three-quarters text-xs"></i>
                </div>
            </div>
            
            <!-- 主页面 -->
            <div id="home-page" class="page active">
                <!-- 飘浮扑克牌效果 -->
                <div class="floating-cards" id="floating-cards"></div>

                <!-- 飘浮筹码效果 -->
                <div class="floating-chips" id="floating-chips"></div>

                <!-- 扑克装饰元素 -->
                <div class="poker-decoration spade">
                    <i class="fas fa-spade-suit"></i>
                </div>
                <div class="poker-decoration heart">
                    <i class="fas fa-heart"></i>
                </div>
                <div class="poker-decoration diamond">
                    <i class="fas fa-diamond"></i>
                </div>
                <div class="poker-decoration club">
                    <i class="fas fa-club"></i>
                </div>

                <div class="p-6 h-full flex flex-col relative z-10">
                    <!-- 头部 -->
                    <div class="flex items-center justify-between mb-6">
                        <div>
                            <h1 class="text-2xl font-bold text-white flex items-center">
                                <div class="mini-card mr-3" style="width: 32px; height: 44px; font-size: 10px;">
                                    <div style="color: #1f2937;">A</div>
                                    <div style="color: #1f2937; font-size: 14px;">♠</div>
                                </div>
                                扑克备忘录
                            </h1>
                            <p class="text-blue-200 text-sm mt-1">记录你的扑克智慧</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <!-- 装饰性扑克筹码 -->
                            <div class="poker-chip w-8 h-8 flex items-center justify-center chip-stack-animated">
                                <div class="w-2 h-2 bg-white rounded-full"></div>
                            </div>
                            <button onclick="showPage('settings-page')" class="text-white p-2">
                                <i class="fas fa-cog text-xl"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 快速录音按钮 -->
                    <div class="flex justify-center mb-6">
                        <button id="quick-record-btn" onclick="startQuickRecord()"
                                class="poker-chip chip-shadow w-20 h-20 flex items-center justify-center text-white text-2xl hover:scale-105 transition-transform">
                            <i class="fas fa-microphone"></i>
                        </button>
                    </div>
                    <p class="text-center text-blue-200 text-sm mb-6">点击开始快速录音</p>

                    <!-- 功能菜单 -->
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <button onclick="showPage('memos-page')" class="poker-card poker-card-shimmer p-4 text-center hover:shadow-lg transition-shadow">
                            <i class="fas fa-list text-2xl text-blue-600 mb-2"></i>
                            <p class="font-semibold text-gray-800">我的备忘录</p>
                            <p class="text-xs text-gray-500" id="memo-count">0 条记录</p>
                        </button>

                        <button onclick="showPage('categories-page')" class="poker-card poker-card-shimmer p-4 text-center hover:shadow-lg transition-shadow">
                            <i class="fas fa-tags text-2xl text-green-600 mb-2 suit-rotate"></i>
                            <p class="font-semibold text-gray-800">分类管理</p>
                            <p class="text-xs text-gray-500">整理分类</p>
                        </button>

                        <button onclick="showPage('search-page')" class="poker-card poker-card-shimmer p-4 text-center hover:shadow-lg transition-shadow">
                            <i class="fas fa-search text-2xl text-purple-600 mb-2"></i>
                            <p class="font-semibold text-gray-800">搜索查找</p>
                            <p class="text-xs text-gray-500">快速定位</p>
                        </button>

                        <button onclick="showPage('record-page')" class="poker-card poker-card-shimmer p-4 text-center hover:shadow-lg transition-shadow">
                            <i class="fas fa-microphone-alt text-2xl text-red-600 mb-2"></i>
                            <p class="font-semibold text-gray-800">录音</p>
                            <p class="text-xs text-gray-500">新建备忘录</p>
                        </button>
                    </div>
                    
                    <!-- 最近记录 -->
                    <div class="flex-1">
                        <h3 class="text-white font-semibold mb-3 flex items-center">
                            <i class="fas fa-clock mr-2"></i>
                            最近记录
                        </h3>
                        <div id="recent-memos" class="space-y-3">
                            <!-- 动态加载的最近记录 -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 录音页面 -->
            <div id="record-page" class="page">
                <!-- 录音页面背景装饰 -->
                <div class="absolute inset-0 opacity-10 pointer-events-none">
                    <div class="absolute top-10 left-10 w-16 h-16 border-2 border-white rounded-full animate-ping"></div>
                    <div class="absolute top-20 right-20 w-8 h-8 border border-white rounded-full animate-pulse"></div>
                    <div class="absolute bottom-20 left-20 w-12 h-12 border border-white rounded-full animate-bounce"></div>
                    <div class="absolute bottom-10 right-10 w-6 h-6 border border-white rounded-full animate-ping"></div>
                </div>

                <div class="p-6 h-full flex flex-col relative z-10">
                    <!-- 导航栏 -->
                    <div class="flex items-center justify-between mb-6">
                        <button onclick="showPage('home-page')" class="text-white p-2">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </button>
                        <h2 class="text-xl font-bold text-white">录音</h2>
                        <div class="w-10"></div>
                    </div>
                    
                    <!-- 录音界面 -->
                    <div class="flex-1 flex flex-col items-center justify-center">
                        <!-- 录音按钮 -->
                        <div class="mb-8 relative">
                            <button id="record-btn" onclick="toggleRecording()"
                                    class="chip-stack w-32 h-32 rounded-full bg-red-500 flex items-center justify-center text-white text-4xl hover:bg-red-600 transition-colors btn-press">
                                <i class="fas fa-microphone"></i>
                            </button>
                        </div>

                        <!-- 录音波形动画 -->
                        <div id="wave-container" class="wave-animation mb-6" style="display: none;">
                            <div class="wave-bar"></div>
                            <div class="wave-bar"></div>
                            <div class="wave-bar"></div>
                            <div class="wave-bar"></div>
                            <div class="wave-bar"></div>
                            <div class="wave-bar"></div>
                            <div class="wave-bar"></div>
                        </div>

                        <!-- 录音状态 -->
                        <div class="text-center mb-8">
                            <p id="record-status" class="text-white text-lg mb-2">点击开始录音</p>
                            <p id="record-time" class="text-blue-200 text-3xl font-mono">00:00</p>
                        </div>
                        
                        <!-- 录音控制 -->
                        <div class="flex space-x-6">
                            <button id="pause-btn" onclick="pauseRecording()" 
                                    class="w-16 h-16 rounded-full bg-yellow-500 flex items-center justify-center text-white text-xl disabled:opacity-50" disabled>
                                <i class="fas fa-pause"></i>
                            </button>
                            <button id="stop-btn" onclick="stopRecording()" 
                                    class="w-16 h-16 rounded-full bg-gray-500 flex items-center justify-center text-white text-xl disabled:opacity-50" disabled>
                                <i class="fas fa-stop"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 录音设置 -->
                    <div class="poker-card p-4">
                        <h3 class="font-semibold text-gray-800 mb-3">录音设置</h3>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm text-gray-600 mb-1">标题</label>
                                <input type="text" id="memo-title" placeholder="输入备忘录标题"
                                       class="w-full p-2 border border-gray-300 rounded-lg">
                            </div>
                            <div>
                                <label class="block text-sm text-gray-600 mb-1">分类</label>
                                <select id="memo-category" class="w-full p-2 border border-gray-300 rounded-lg">
                                    <option value="texas">德州扑克</option>
                                    <option value="omaha">奥马哈</option>
                                    <option value="stud">七张牌</option>
                                    <option value="other">其他</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm text-gray-600 mb-1">选择扑克牌Logo</label>
                                <div class="flex items-center space-x-3">
                                    <div id="selected-card-display" class="mini-card">
                                        <div style="font-size: 6px; color: #dc2626;">A</div>
                                        <div style="font-size: 10px; color: #dc2626;">♥</div>
                                    </div>
                                    <button type="button" onclick="showCardSelector()"
                                            class="px-3 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors">
                                        选择扑克牌
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 备忘录列表页面 -->
            <div id="memos-page" class="page">
                <!-- 筹码装饰背景 -->
                <div class="absolute inset-0 opacity-10 pointer-events-none">
                    <div class="absolute top-16 left-8 poker-chip w-12 h-12 chip-stack-animated"></div>
                    <div class="absolute top-32 right-12 poker-chip w-8 h-8 chip-stack-animated" style="animation-delay: 0.5s;"></div>
                    <div class="absolute bottom-32 left-16 poker-chip w-10 h-10 chip-stack-animated" style="animation-delay: 1s;"></div>
                    <div class="absolute bottom-16 right-8 poker-chip w-6 h-6 chip-stack-animated" style="animation-delay: 1.5s;"></div>
                </div>

                <div class="p-6 h-full flex flex-col relative z-10">
                    <!-- 导航栏 -->
                    <div class="flex items-center justify-between mb-6">
                        <button onclick="showPage('home-page')" class="text-white p-2">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </button>
                        <h2 class="text-xl font-bold text-white">我的备忘录</h2>
                        <div class="flex items-center space-x-2">
                            <button id="batch-delete-btn" onclick="toggleBatchDelete()" class="text-white p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors">
                                <i class="fas fa-trash text-xl"></i>
                            </button>
                            <button onclick="showPage('record-page')" class="text-white p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors">
                                <i class="fas fa-plus text-xl"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 筛选栏 -->
                    <div class="flex space-x-2 mb-4">
                        <button class="px-3 py-1 bg-white rounded-full text-sm font-medium">全部</button>
                        <button class="px-3 py-1 bg-blue-100 text-blue-600 rounded-full text-sm">德州扑克</button>
                        <button class="px-3 py-1 bg-blue-100 text-blue-600 rounded-full text-sm">奥马哈</button>
                        <button class="px-3 py-1 bg-blue-100 text-blue-600 rounded-full text-sm">收藏</button>
                    </div>
                    
                    <!-- 备忘录列表 -->
                    <div class="flex-1 space-y-3" id="memos-list">
                        <!-- 示例备忘录项 -->
                        <div class="poker-card p-4">
                            <div class="flex items-start justify-between mb-2">
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-800 flex items-center">
                                        <i class="fas fa-heart card-suit-red mr-2"></i>
                                        德州扑克策略分析
                                        <i class="fas fa-star text-yellow-500 ml-2"></i>
                                    </h3>
                                    <p class="text-sm text-gray-500 mt-1">2024-01-15 14:30 · 德州扑克</p>
                                </div>
                                <button class="text-blue-600 p-2">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4 text-sm text-gray-500">
                                    <span><i class="fas fa-clock mr-1"></i>2:34</span>
                                    <span><i class="fas fa-play mr-1"></i>5次</span>
                                </div>
                                <button onclick="showPage('player-page')" class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
                                    <i class="fas fa-play mr-1"></i>播放
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 播放器页面 -->
            <div id="player-page" class="page">
                <!-- 扑克桌面装饰 -->
                <div class="absolute inset-0 opacity-20 pointer-events-none">
                    <!-- 扑克桌面圆形边框 -->
                    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 border-4 border-yellow-400 rounded-full opacity-30"></div>
                    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 border-2 border-yellow-300 rounded-full opacity-20"></div>

                    <!-- 装饰性扑克牌 -->
                    <div class="absolute top-10 left-10 mini-card" style="color: #dc2626; opacity: 0.3;">
                        <div style="font-size: 6px;">A</div>
                        <div style="font-size: 10px;">♥</div>
                    </div>
                    <div class="absolute top-10 right-10 mini-card" style="color: #1f2937; opacity: 0.3;">
                        <div style="font-size: 6px;">K</div>
                        <div style="font-size: 10px;">♠</div>
                    </div>
                    <div class="absolute bottom-10 left-10 mini-card" style="color: #dc2626; opacity: 0.3;">
                        <div style="font-size: 6px;">Q</div>
                        <div style="font-size: 10px;">♦</div>
                    </div>
                    <div class="absolute bottom-10 right-10 mini-card" style="color: #1f2937; opacity: 0.3;">
                        <div style="font-size: 6px;">J</div>
                        <div style="font-size: 10px;">♣</div>
                    </div>
                </div>

                <div class="p-6 h-full flex flex-col relative z-10">
                    <!-- 导航栏 -->
                    <div class="flex items-center justify-between mb-6">
                        <button onclick="showPage('memos-page')" class="text-white p-2">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </button>
                        <h2 class="text-xl font-bold text-white">播放</h2>
                        <button class="text-white p-2">
                            <i class="fas fa-share text-xl"></i>
                        </button>
                    </div>

                    <!-- 备忘录信息 -->
                    <div class="poker-card table-gloss p-6 mb-6">
                        <div class="text-center">
                            <div id="player-card-logo" class="w-24 h-24 mx-auto mb-4 flex items-center justify-center">
                                <!-- 扑克牌logo将在这里动态生成 -->
                                <div class="playing-card" style="width: 80px; height: 112px;">
                                    <div class="card-value" style="color: #dc2626; font-size: 16px;">A</div>
                                    <div class="card-suit" style="color: #dc2626; font-size: 32px;">♥</div>
                                    <div class="card-suit-small" style="color: #dc2626;">♥</div>
                                </div>
                            </div>
                            <h3 id="player-title" class="text-xl font-bold text-gray-800 mb-2">德州扑克策略分析</h3>
                            <p id="player-info" class="text-gray-500 text-sm">德州扑克 · 2024-01-15</p>
                        </div>
                    </div>

                    <!-- 播放进度 -->
                    <div class="poker-card p-6 mb-6">
                        <div class="flex items-center justify-between text-sm text-gray-500 mb-2">
                            <span id="current-time">0:00</span>
                            <span id="total-time">2:34</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
                            <div id="progress-bar" class="bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
                        </div>

                        <!-- 播放控制 -->
                        <div class="flex items-center justify-center space-x-6">
                            <button id="prev-btn" class="text-gray-400 text-xl">
                                <i class="fas fa-step-backward"></i>
                            </button>
                            <button id="rewind-btn" class="text-gray-600 text-xl">
                                <i class="fas fa-backward"></i>
                            </button>
                            <button id="play-pause-btn" onclick="togglePlayback()"
                                    class="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center text-white text-xl">
                                <i class="fas fa-play"></i>
                            </button>
                            <button id="forward-btn" class="text-gray-600 text-xl">
                                <i class="fas fa-forward"></i>
                            </button>
                            <button id="next-btn" class="text-gray-400 text-xl">
                                <i class="fas fa-step-forward"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 播放选项 -->
                    <div class="poker-card p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <button id="speed-btn" onclick="changePlaybackSpeed()" class="flex items-center space-x-2 text-gray-600">
                                    <i class="fas fa-tachometer-alt"></i>
                                    <span id="speed-text">1.0x</span>
                                </button>
                                <button class="text-gray-600">
                                    <i class="fas fa-repeat"></i>
                                </button>
                            </div>
                            <div class="flex items-center space-x-4">
                                <button onclick="toggleStar()" class="text-yellow-500">
                                    <i id="star-icon" class="fas fa-star"></i>
                                </button>
                                <button class="text-gray-600">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分类管理页面 -->
            <div id="categories-page" class="page">
                <!-- 扑克花色装饰 -->
                <div class="absolute inset-0 opacity-5 pointer-events-none poker-suits-pattern"></div>

                <div class="p-6 h-full flex flex-col relative z-10">
                    <!-- 导航栏 -->
                    <div class="flex items-center justify-between mb-6">
                        <button onclick="showPage('home-page')" class="text-white p-2">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </button>
                        <h2 class="text-xl font-bold text-white">分类管理</h2>
                        <button onclick="addNewCategory()" class="text-white p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors">
                            <i class="fas fa-plus text-xl"></i>
                        </button>
                    </div>

                    <!-- 分类列表 -->
                    <div class="space-y-3" id="categories-list">
                        <div class="poker-card p-4 flex items-center justify-between card-pattern-bg">
                            <div class="flex items-center">
                                <div class="playing-card mr-4" style="width: 48px; height: 64px;">
                                    <div class="card-value" style="color: #dc2626; font-size: 12px;">A</div>
                                    <div class="card-suit" style="color: #dc2626; font-size: 20px;">♥</div>
                                    <div class="card-suit-small" style="color: #dc2626;">♥</div>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-800">德州扑克</h3>
                                    <p class="text-sm text-gray-500">2 条备忘录</p>
                                </div>
                            </div>
                            <button class="text-gray-400 hover:text-gray-600 transition-colors">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>

                        <div class="poker-card p-4 flex items-center justify-between card-pattern-bg">
                            <div class="flex items-center">
                                <div class="playing-card mr-4" style="width: 48px; height: 64px;">
                                    <div class="card-value" style="color: #dc2626; font-size: 12px;">K</div>
                                    <div class="card-suit" style="color: #dc2626; font-size: 20px;">♦</div>
                                    <div class="card-suit-small" style="color: #dc2626;">♦</div>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-800">奥马哈</h3>
                                    <p class="text-sm text-gray-500">1 条备忘录</p>
                                </div>
                            </div>
                            <button class="text-gray-400 hover:text-gray-600 transition-colors">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>

                        <div class="poker-card p-4 flex items-center justify-between card-pattern-bg">
                            <div class="flex items-center">
                                <div class="playing-card mr-4" style="width: 48px; height: 64px;">
                                    <div class="card-value" style="color: #1f2937; font-size: 12px;">J</div>
                                    <div class="card-suit" style="color: #1f2937; font-size: 20px;">♣</div>
                                    <div class="card-suit-small" style="color: #1f2937;">♣</div>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-800">七张牌</h3>
                                    <p class="text-sm text-gray-500">0 条备忘录</p>
                                </div>
                            </div>
                            <button class="text-gray-400 hover:text-gray-600 transition-colors">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>

                        <div class="poker-card p-4 flex items-center justify-between card-pattern-bg">
                            <div class="flex items-center">
                                <div class="playing-card mr-4" style="width: 48px; height: 64px;">
                                    <div class="card-value" style="color: #1f2937; font-size: 12px;">Q</div>
                                    <div class="card-suit" style="color: #1f2937; font-size: 20px;">♠</div>
                                    <div class="card-suit-small" style="color: #1f2937;">♠</div>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-800">其他</h3>
                                    <p class="text-sm text-gray-500">1 条备忘录</p>
                                </div>
                            </div>
                            <button class="text-gray-400 hover:text-gray-600 transition-colors">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索页面 -->
            <div id="search-page" class="page">
                <!-- 搜索页面装饰 -->
                <div class="absolute inset-0 opacity-10 pointer-events-none">
                    <div class="absolute top-20 left-1/4 w-4 h-4 bg-white rounded-full animate-pulse"></div>
                    <div class="absolute top-40 right-1/4 w-6 h-6 bg-white rounded-full animate-ping"></div>
                    <div class="absolute bottom-40 left-1/3 w-3 h-3 bg-white rounded-full animate-bounce"></div>
                    <div class="absolute bottom-20 right-1/3 w-5 h-5 bg-white rounded-full animate-pulse"></div>
                </div>

                <div class="p-6 h-full flex flex-col relative z-10">
                    <!-- 导航栏 -->
                    <div class="flex items-center justify-between mb-6">
                        <button onclick="showPage('home-page')" class="text-white p-2">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </button>
                        <h2 class="text-xl font-bold text-white">搜索</h2>
                        <div class="w-10"></div>
                    </div>

                    <!-- 搜索框 -->
                    <div class="poker-card p-4 mb-6">
                        <div class="relative">
                            <input type="text" id="search-input" placeholder="搜索备忘录标题..."
                                   oninput="performSearch()"
                                   onkeypress="handleSearchKeyPress(event)"
                                   onfocus="showSearchSuggestions()"
                                   onblur="hideSearchSuggestions()"
                                   class="w-full p-3 pl-10 pr-10 border border-gray-300 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <button id="clear-search" onclick="clearSearch()"
                                    class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 hidden">
                                <i class="fas fa-times"></i>
                            </button>

                            <!-- 搜索建议下拉框 -->
                            <div id="search-suggestions" class="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg mt-1 hidden z-10">
                                <!-- 搜索建议将在这里动态生成 -->
                            </div>
                        </div>

                        <!-- 搜索选项 -->
                        <div class="mt-3 flex items-center space-x-4 text-sm">
                            <label class="flex items-center">
                                <input type="checkbox" id="search-title" checked class="mr-2">
                                <span class="text-gray-700">标题</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="search-category" class="mr-2">
                                <span class="text-gray-700">分类</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" id="search-starred" class="mr-2">
                                <span class="text-gray-700">仅收藏</span>
                            </label>
                        </div>
                    </div>

                    <!-- 快速筛选 -->
                    <div class="mb-6">
                        <h3 class="text-white font-semibold mb-3">快速筛选</h3>
                        <div class="grid grid-cols-2 gap-3">
                            <button onclick="quickFilter('starred')"
                                    class="poker-card p-3 text-center hover:shadow-lg transition-shadow"
                                    id="filter-starred">
                                <div class="mini-card mx-auto mb-2" style="color: #eab308;">
                                    <div style="font-size: 6px;">★</div>
                                    <div style="font-size: 10px;">♠</div>
                                </div>
                                <p class="text-sm font-medium">收藏的</p>
                            </button>
                            <button onclick="quickFilter('recent')"
                                    class="poker-card p-3 text-center hover:shadow-lg transition-shadow"
                                    id="filter-recent">
                                <div class="mini-card mx-auto mb-2" style="color: #3b82f6;">
                                    <div style="font-size: 6px;">10</div>
                                    <div style="font-size: 10px;">♥</div>
                                </div>
                                <p class="text-sm font-medium">最近播放</p>
                            </button>
                            <button onclick="quickFilter('today')"
                                    class="poker-card p-3 text-center hover:shadow-lg transition-shadow"
                                    id="filter-today">
                                <div class="mini-card mx-auto mb-2" style="color: #10b981;">
                                    <div style="font-size: 6px;">J</div>
                                    <div style="font-size: 10px;">♦</div>
                                </div>
                                <p class="text-sm font-medium">今天</p>
                            </button>
                            <button onclick="quickFilter('week')"
                                    class="poker-card p-3 text-center hover:shadow-lg transition-shadow"
                                    id="filter-week">
                                <div class="mini-card mx-auto mb-2" style="color: #8b5cf6;">
                                    <div style="font-size: 6px;">Q</div>
                                    <div style="font-size: 10px;">♣</div>
                                </div>
                                <p class="text-sm font-medium">本周</p>
                            </button>
                        </div>

                        <!-- 分类筛选 -->
                        <div class="mt-4">
                            <h4 class="text-white font-medium mb-2 text-sm">按分类筛选</h4>
                            <div class="flex flex-wrap gap-2" id="category-filters">
                                <!-- 分类筛选按钮将在这里动态生成 -->
                            </div>
                        </div>
                    </div>

                    <!-- 搜索结果 -->
                    <div class="flex-1">
                        <!-- 搜索结果头部 -->
                        <div id="search-header" class="hidden mb-4">
                            <div class="flex items-center justify-between">
                                <h4 class="text-white font-semibold">搜索结果</h4>
                                <div class="flex items-center space-x-2">
                                    <span id="results-count" class="text-blue-200 text-sm"></span>
                                    <button onclick="clearAllFilters()" class="text-blue-200 text-sm hover:text-white">
                                        清除筛选
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 搜索结果列表 -->
                        <div id="search-results">
                            <div class="text-center py-12">
                                <i class="fas fa-search text-4xl text-white opacity-30 mb-4"></i>
                                <p class="text-blue-200">输入关键词开始搜索</p>
                                <p class="text-blue-300 text-sm mt-2">或使用上方的快速筛选</p>
                            </div>
                        </div>

                        <!-- 无结果状态 -->
                        <div id="no-results" class="hidden text-center py-12">
                            <i class="fas fa-search-minus text-4xl text-white opacity-30 mb-4"></i>
                            <p class="text-blue-200">没有找到匹配的备忘录</p>
                            <p class="text-blue-300 text-sm mt-2">尝试调整搜索条件</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 设置页面 -->
            <div id="settings-page" class="page">
                <!-- 金色装饰边框 -->
                <div class="absolute top-4 left-4 right-4 bottom-4 border-2 border-yellow-400 rounded-lg opacity-20 pointer-events-none"></div>

                <div class="p-6 h-full flex flex-col relative z-10">
                    <!-- 导航栏 -->
                    <div class="flex items-center justify-between mb-6">
                        <button onclick="showPage('home-page')" class="text-white p-2">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </button>
                        <h2 class="text-xl font-bold text-white">设置</h2>
                        <div class="w-10"></div>
                    </div>

                    <!-- 设置选项 -->
                    <div class="space-y-4">
                        <div class="poker-card p-4 card-pattern-bg">
                            <h3 class="font-semibold text-gray-800 mb-3 flex items-center">
                                <div class="mini-card mr-2" style="color: #dc2626;">
                                    <div style="font-size: 6px;">A</div>
                                    <div style="font-size: 10px;">♥</div>
                                </div>
                                录音设置
                            </h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-700">音频质量</span>
                                    <select class="p-2 border border-gray-300 rounded">
                                        <option>标准</option>
                                        <option>高质量</option>
                                    </select>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-700">最大录音时长</span>
                                    <select class="p-2 border border-gray-300 rounded">
                                        <option>30分钟</option>
                                        <option>1小时</option>
                                        <option>2小时</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="poker-card p-4">
                            <h3 class="font-semibold text-gray-800 mb-3">播放设置</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-700">默认播放速度</span>
                                    <select class="p-2 border border-gray-300 rounded">
                                        <option>0.5x</option>
                                        <option selected>1.0x</option>
                                        <option>1.5x</option>
                                        <option>2.0x</option>
                                    </select>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>

            <!-- 底部安全区域 -->
            <div class="safe-area-bottom"></div>
        </div>
    </div>

    <!-- 扑克牌选择器模态框 -->
    <div id="card-selector-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white rounded-lg p-6 m-4 max-w-sm w-full">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">选择扑克牌Logo</h3>
                <button onclick="hideCardSelector()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="card-selector-grid" class="card-selector-grid">
                <!-- 扑克牌网格将在这里动态生成 -->
            </div>
            <div class="flex justify-end mt-4">
                <button onclick="hideCardSelector()"
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    确定
                </button>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
