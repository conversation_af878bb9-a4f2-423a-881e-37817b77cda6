# 扑克语音备忘录App需求文档

## 1. 项目概述

### 1.1 项目名称
扑克语音备忘录 (Poker Voice Memo)

### 1.2 项目描述
一款专为扑克爱好者设计的语音备忘录应用，帮助用户记录扑克游戏中的重要信息、策略思考、对手分析等内容。应用采用扑克主题设计，提供直观的语音录制和管理功能。

### 1.3 目标用户
- 扑克爱好者和职业玩家
- 需要记录游戏策略和经验的用户
- 喜欢语音记录方式的用户

## 2. 用户细分

### 2.1 主要用户群体

#### 2.1.1 休闲扑克玩家
- **特征**: 偶尔参与扑克游戏，主要为娱乐
- **需求**: 简单易用的录音功能，记录有趣的游戏时刻
- **使用场景**: 家庭聚会、朋友聚会时的扑克游戏

#### 2.1.2 认真扑克玩家
- **特征**: 定期参与扑克游戏，希望提升技能
- **需求**: 详细的分类管理，能够按游戏类型、日期等维度整理备忘录
- **使用场景**: 线下扑克俱乐部、定期的扑克聚会


### 2.2 用户痛点
- 传统文字记录在游戏中不便操作
- 缺乏专门针对扑克的记录工具
- 难以快速回顾和查找历史记录

## 3. 核心功能需求

### 3.1 语音录制功能
- **快速录制**: 一键开始/停止录音
- **录音质量**: 支持多种音质选择（标准、高质量）
- **录音时长**: 支持长时间录音（最长2小时）
- **实时显示**: 录音时显示时长

### 3.2 备忘录管理
- **分类管理**: 按游戏类型分类（德州扑克、奥马哈、七张牌等）
- **标签系统**: 自定义标签（策略、对手分析、牌局回顾等）
- **时间排序**: 按创建时间、修改时间排序
- **收藏功能**: 标记重要的备忘录
- **删除管理**: 支持单个删除和批量删除

### 3.3 播放功能
- **基础播放**: 播放、暂停、停止
- **进度控制**: 拖拽进度条快速定位
- **播放速度**: 支持0.5x、1x、1.5x、2x播放速度
- **循环播放**: 支持单曲循环和列表循环


### 3.4 搜索功能
- **标题搜索**: 根据备忘录标题搜索
- **标签搜索**: 根据标签快速筛选
- **日期筛选**: 按日期范围筛选
- **分类筛选**: 按游戏类型筛选
- **组合搜索**: 支持多条件组合搜索

### 3.5 数据管理
- **本地存储**: 所有数据存储在本地设备

## 4. 非功能需求

### 4.1 性能需求
- **启动时间**: 应用启动时间不超过3秒
- **录音延迟**: 点击录音按钮到开始录音延迟不超过0.5秒
- **播放延迟**: 点击播放按钮到开始播放延迟不超过1秒
- **搜索响应**: 搜索结果返回时间不超过2秒
- **内存使用**: 正常使用时内存占用不超过100MB

### 4.2 可用性需求
- **界面简洁**: 主要功能一键可达
- **操作直观**: 符合iOS设计规范和用户习惯
- **反馈及时**: 所有操作都有明确的视觉或听觉反馈
- **错误处理**: 友好的错误提示和恢复机制
- **无障碍**: 支持VoiceOver等无障碍功能

### 4.3 兼容性需求
- **iOS版本**: 支持iOS 14.0及以上版本
- **设备兼容**: 支持iPhone 8及以上机型
- **屏幕适配**: 适配不同尺寸的iPhone屏幕
- **横竖屏**: 主要支持竖屏，播放界面支持横屏

### 4.4 安全需求
- **数据隐私**: 所有数据仅存储在本地设备
- **权限管理**: 仅申请必要的麦克风权限
- **数据加密**: 敏感数据采用本地加密存储

## 5. 数据模型

### 5.1 备忘录实体 (VoiceMemo)
```
{
  id: String,              // 唯一标识符
  title: String,           // 备忘录标题
  audioFilePath: String,   // 音频文件路径
  duration: Number,        // 录音时长（秒）
  fileSize: Number,        // 文件大小（字节）
  category: String,        // 游戏类型分类
  tags: Array<String>,     // 标签数组
  isStarred: Boolean,      // 是否收藏
  createdAt: Date,         // 创建时间
  updatedAt: Date,         // 最后修改时间
  playCount: Number,       // 播放次数
  lastPlayedAt: Date       // 最后播放时间
}
```

### 5.2 分类实体 (Category)
```
{
  id: String,              // 分类ID
  name: String,            // 分类名称
  icon: String,            // 分类图标
  color: String,           // 分类颜色
  isDefault: Boolean,      // 是否为默认分类
  createdAt: Date          // 创建时间
}
```

### 5.3 标签实体 (Tag)
```
{
  id: String,              // 标签ID
  name: String,            // 标签名称
  color: String,           // 标签颜色
  usageCount: Number,      // 使用次数
  createdAt: Date          // 创建时间
}
```

### 5.4 应用设置 (AppSettings)
```
{
  audioQuality: String,    // 音频质量设置
  playbackSpeed: Number,   // 默认播放速度
  autoBackup: Boolean,     // 自动备份开关
  themeMode: String,       // 主题模式
  sortOrder: String,       // 默认排序方式
  maxRecordingTime: Number // 最大录音时长
}
```

## 6. 技术架构

### 6.1 前端技术
- **框架**: 原生HTML5 + CSS3 + JavaScript
- **样式**: Tailwind CSS
- **音频处理**: Web Audio API
- **本地存储**: IndexedDB
- **文件管理**: File API

### 6.2 数据存储
- **主要存储**: IndexedDB（结构化数据）
- **文件存储**: 本地文件系统（音频文件）
- **配置存储**: localStorage（应用设置）

### 6.3 核心模块
- **录音模块**: 负责音频录制和文件管理
- **播放模块**: 负责音频播放和控制
- **数据模块**: 负责数据的增删改查
- **搜索模块**: 负责搜索和筛选功能
- **UI模块**: 负责用户界面交互

## 7. 界面设计要求

### 7.1 设计风格
- **主题**: 扑克元素融入现代iOS设计
- **色彩**: 以扑克经典的红、黑、金色为主色调
- **图标**: 融入扑克牌、筹码等元素
- **字体**: 使用iOS系统字体确保可读性

### 7.2 主要界面
1. **主界面**: 备忘录列表，快速录音按钮
2. **录音界面**: 录音控制，实时波形显示
3. **播放界面**: 播放控制，进度显示
4. **分类管理**: 分类列表，添加/编辑分类
5. **搜索界面**: 搜索框，筛选选项
6. **设置界面**: 应用设置，数据管理

### 7.3 交互设计
- **手势支持**: 滑动删除，长按选择
- **动画效果**: 平滑的过渡动画
- **反馈机制**: 触觉反馈，视觉反馈
- **快捷操作**: 3D Touch支持（如适用）

## 8. 开发优先级

### 8.1 第一阶段（MVP）
- 基础录音功能
- 简单的播放功能
- 基本的列表管理
- 简单的分类功能

### 8.2 第二阶段
- 高级播放控制
- 标签系统
- 搜索功能
- 数据备份

### 8.3 第三阶段
- 高级筛选
- 统计功能
- 界面优化
- 性能优化

## 9. 验收标准

### 9.1 功能验收
- 所有核心功能正常工作
- 界面响应流畅无卡顿
- 数据存储和读取正确
- 音频质量满足要求

### 9.2 用户体验验收
- 界面美观符合设计规范
- 操作逻辑清晰易懂
- 错误处理友好
- 性能指标达标

### 9.3 兼容性验收
- 在目标设备上正常运行
- 不同屏幕尺寸适配良好
- 系统权限申请正常
- 数据迁移功能正常
