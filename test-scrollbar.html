<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>滚动条隐藏测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">
    <style>
        .test-container {
            width: 300px;
            height: 200px;
            border: 2px solid #ccc;
            margin: 20px;
            padding: 10px;
        }
    </style>
</head>
<body class="p-8">
    <h1 class="text-2xl font-bold mb-6">滚动条隐藏测试</h1>
    
    <div class="grid grid-cols-2 gap-4">
        <!-- 测试1: overflow-y-auto -->
        <div class="test-container overflow-y-auto">
            <h3 class="font-semibold mb-2">overflow-y-auto 测试</h3>
            <div class="space-y-2">
                <div class="p-2 bg-blue-100">项目 1</div>
                <div class="p-2 bg-blue-100">项目 2</div>
                <div class="p-2 bg-blue-100">项目 3</div>
                <div class="p-2 bg-blue-100">项目 4</div>
                <div class="p-2 bg-blue-100">项目 5</div>
                <div class="p-2 bg-blue-100">项目 6</div>
                <div class="p-2 bg-blue-100">项目 7</div>
                <div class="p-2 bg-blue-100">项目 8</div>
                <div class="p-2 bg-blue-100">项目 9</div>
                <div class="p-2 bg-blue-100">项目 10</div>
            </div>
        </div>
        
        <!-- 测试2: max-h + overflow-y-auto -->
        <div class="test-container">
            <h3 class="font-semibold mb-2">max-h + overflow-y-auto 测试</h3>
            <div class="max-h-32 overflow-y-auto space-y-2">
                <div class="p-2 bg-green-100">项目 A</div>
                <div class="p-2 bg-green-100">项目 B</div>
                <div class="p-2 bg-green-100">项目 C</div>
                <div class="p-2 bg-green-100">项目 D</div>
                <div class="p-2 bg-green-100">项目 E</div>
                <div class="p-2 bg-green-100">项目 F</div>
                <div class="p-2 bg-green-100">项目 G</div>
                <div class="p-2 bg-green-100">项目 H</div>
            </div>
        </div>
        
        <!-- 测试3: CSS overflow -->
        <div class="test-container" style="overflow-y: auto;">
            <h3 class="font-semibold mb-2">CSS overflow 测试</h3>
            <div class="space-y-2">
                <div class="p-2 bg-red-100">CSS 项目 1</div>
                <div class="p-2 bg-red-100">CSS 项目 2</div>
                <div class="p-2 bg-red-100">CSS 项目 3</div>
                <div class="p-2 bg-red-100">CSS 项目 4</div>
                <div class="p-2 bg-red-100">CSS 项目 5</div>
                <div class="p-2 bg-red-100">CSS 项目 6</div>
                <div class="p-2 bg-red-100">CSS 项目 7</div>
                <div class="p-2 bg-red-100">CSS 项目 8</div>
            </div>
        </div>
        
        <!-- 测试4: 动态添加内容 -->
        <div class="test-container">
            <h3 class="font-semibold mb-2">动态内容测试</h3>
            <button onclick="addContent()" class="mb-2 px-3 py-1 bg-blue-500 text-white rounded">添加内容</button>
            <div id="dynamic-content" class="max-h-24 overflow-y-auto space-y-1">
                <div class="p-1 bg-yellow-100">初始项目</div>
            </div>
        </div>
    </div>
    
    <div class="mt-8 p-4 bg-gray-100 rounded">
        <h3 class="font-semibold mb-2">说明：</h3>
        <p>如果滚动条隐藏成功，上面的容器应该可以滚动但看不到滚动条。</p>
        <p>所有容器的内容都超出了容器高度，应该可以滚动但不显示滚动条。</p>
    </div>
    
    <script>
        let itemCount = 1;
        function addContent() {
            const container = document.getElementById('dynamic-content');
            const newItem = document.createElement('div');
            newItem.className = 'p-1 bg-yellow-100';
            newItem.textContent = `动态项目 ${++itemCount}`;
            container.appendChild(newItem);
        }
        
        // 确保页面加载后隐藏滚动条
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const elements = document.querySelectorAll('*');
                elements.forEach(el => {
                    const style = window.getComputedStyle(el);
                    if (style.overflowY === 'auto' || style.overflow === 'auto') {
                        el.style.setProperty('scrollbar-width', 'none', 'important');
                        el.style.setProperty('-ms-overflow-style', 'none', 'important');
                    }
                });
            }, 100);
        });
    </script>
</body>
</html>
