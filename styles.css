/* 扑克语音备忘录 - 增强样式 */

/* 扑克主题背景图案 */
.poker-pattern {
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 2px, transparent 2px);
    background-size: 40px 40px;
}

/* 扑克桌面背景 */
.poker-table-bg {
    background:
        linear-gradient(135deg, rgba(22, 163, 74, 0.9) 0%, rgba(21, 128, 61, 0.9) 100%),
        url('https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}

/* 赌场氛围背景 */
.casino-bg {
    background:
        linear-gradient(135deg, rgba(30, 58, 138, 0.95) 0%, rgba(124, 45, 18, 0.95) 100%),
        url('https://images.unsplash.com/photo-1596838132731-3301c3fd4317?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}

/* 扑克牌背景纹理 */
.poker-cards-bg {
    background:
        linear-gradient(135deg, rgba(30, 58, 138, 0.9) 0%, rgba(124, 45, 18, 0.9) 100%),
        url('https://images.unsplash.com/photo-1606092195730-5d7b9af1efc5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}

/* 筹码背景 */
.poker-chips-bg {
    background:
        linear-gradient(135deg, rgba(30, 58, 138, 0.9) 0%, rgba(124, 45, 18, 0.9) 100%),
        url('https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
}

/* 扑克花色装饰背景 */
.poker-suits-pattern {
    background-image:
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M30 15c-8.284 0-15 6.716-15 15s6.716 15 15 15 15-6.716 15-15-6.716-15-15-15zm0 25c-5.523 0-10-4.477-10-10s4.477-10 10-10 10 4.477 10 10-4.477 10-10 10z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-size: 60px 60px;
}

/* 动态扑克牌飘落效果 */
.floating-cards {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;
}

.floating-card {
    position: absolute;
    width: 30px;
    height: 42px;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid rgba(226, 232, 240, 0.5);
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    font-weight: bold;
    opacity: 0.3;
    animation: floatDown 15s linear infinite;
}

@keyframes floatDown {
    0% {
        transform: translateY(-100px) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 0.3;
    }
    90% {
        opacity: 0.3;
    }
    100% {
        transform: translateY(calc(100vh + 100px)) rotate(360deg);
        opacity: 0;
    }
}

/* 扑克筹码飘浮效果 */
.floating-chips {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;
}

.floating-chip {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: radial-gradient(circle, #fbbf24 0%, #f59e0b 50%, #d97706 100%);
    border: 2px solid #ffffff;
    opacity: 0.4;
    animation: floatUp 12s linear infinite;
}

@keyframes floatUp {
    0% {
        transform: translateY(calc(100vh + 50px)) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 0.4;
    }
    90% {
        opacity: 0.4;
    }
    100% {
        transform: translateY(-50px) rotate(-360deg);
        opacity: 0;
    }
}

/* 扑克桌面纹理 */
.felt-texture {
    background:
        radial-gradient(ellipse at center, #16a34a 0%, #15803d 70%, #166534 100%),
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 2px,
            rgba(255, 255, 255, 0.02) 2px,
            rgba(255, 255, 255, 0.02) 4px
        );
}

/* 金色装饰边框 */
.golden-border {
    border: 2px solid;
    border-image: linear-gradient(45deg, #fbbf24, #f59e0b, #d97706, #fbbf24) 1;
    position: relative;
}

.golden-border::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    background: linear-gradient(45deg, #fbbf24, #f59e0b, #d97706, #fbbf24);
    border-radius: inherit;
    z-index: -1;
    opacity: 0.3;
    filter: blur(2px);
}

/* 扑克牌花色动画 */
.suit-animation {
    animation: suitFloat 3s ease-in-out infinite;
}

@keyframes suitFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

/* 筹码堆叠效果 */
.chip-stack {
    position: relative;
}

.chip-stack::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 2px;
    right: -2px;
    bottom: 2px;
    background: radial-gradient(circle, #f59e0b 0%, #d97706 50%, #b45309 100%);
    border-radius: 50%;
    z-index: -1;
    opacity: 0.7;
}

.chip-stack::after {
    content: '';
    position: absolute;
    top: -4px;
    left: 4px;
    right: -4px;
    bottom: 4px;
    background: radial-gradient(circle, #f59e0b 0%, #d97706 50%, #b45309 100%);
    border-radius: 50%;
    z-index: -2;
    opacity: 0.4;
}

/* 录音波形动画 */
.wave-animation {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2px;
}

.wave-bar {
    width: 3px;
    background: linear-gradient(to top, #ef4444, #f87171);
    border-radius: 2px;
    animation: wave 1.5s ease-in-out infinite;
}

.wave-bar:nth-child(1) { height: 10px; animation-delay: 0s; }
.wave-bar:nth-child(2) { height: 20px; animation-delay: 0.1s; }
.wave-bar:nth-child(3) { height: 15px; animation-delay: 0.2s; }
.wave-bar:nth-child(4) { height: 25px; animation-delay: 0.3s; }
.wave-bar:nth-child(5) { height: 18px; animation-delay: 0.4s; }
.wave-bar:nth-child(6) { height: 22px; animation-delay: 0.5s; }
.wave-bar:nth-child(7) { height: 12px; animation-delay: 0.6s; }

@keyframes wave {
    0%, 100% { transform: scaleY(0.5); opacity: 0.7; }
    50% { transform: scaleY(1); opacity: 1; }
}

/* 卡片悬浮效果 */
.poker-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.poker-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.poker-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.poker-card:hover::before {
    left: 100%;
}

/* 按钮点击效果 */
.btn-press {
    transition: all 0.1s ease;
}

.btn-press:active {
    transform: scale(0.95);
}

/* 进度条动画 */
.progress-glow {
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
    transition: box-shadow 0.3s ease;
}

/* 扑克牌翻转效果 */
.card-flip {
    perspective: 1000px;
}

.card-flip-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
}

.card-flip:hover .card-flip-inner {
    transform: rotateY(180deg);
}

.card-flip-front, .card-flip-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 12px;
}

.card-flip-back {
    transform: rotateY(180deg);
    background: linear-gradient(135deg, #1e3a8a 0%, #7c2d12 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 音频可视化效果 */
.audio-visualizer {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
    gap: 2px;
}

.visualizer-bar {
    width: 4px;
    background: linear-gradient(to top, #3b82f6, #60a5fa);
    border-radius: 2px;
    transition: height 0.1s ease;
}

/* 扑克主题装饰元素 */
.poker-decoration {
    position: absolute;
    opacity: 0.1;
    pointer-events: none;
}

.poker-decoration.spade {
    top: 10%;
    right: 10%;
    font-size: 2rem;
    color: #1f2937;
    animation: decorationFloat 4s ease-in-out infinite;
}

.poker-decoration.heart {
    bottom: 20%;
    left: 15%;
    font-size: 1.5rem;
    color: #dc2626;
    animation: decorationFloat 3s ease-in-out infinite reverse;
}

.poker-decoration.diamond {
    top: 30%;
    left: 10%;
    font-size: 1.8rem;
    color: #dc2626;
    animation: decorationFloat 3.5s ease-in-out infinite;
}

.poker-decoration.club {
    bottom: 10%;
    right: 20%;
    font-size: 2.2rem;
    color: #1f2937;
    animation: decorationFloat 4.5s ease-in-out infinite reverse;
}

@keyframes decorationFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-10px) rotate(5deg); }
    50% { transform: translateY(-5px) rotate(-3deg); }
    75% { transform: translateY(-15px) rotate(3deg); }
}

/* 毛玻璃效果 */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 渐变文字效果 */
.gradient-text {
    background: linear-gradient(135deg, #fbbf24, #f59e0b, #d97706);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 脉冲动画增强 */
.pulse-enhanced {
    animation: pulseEnhanced 2s ease-in-out infinite;
}

@keyframes pulseEnhanced {
    0% { 
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
}

/* 加载动画 */
.loading-spinner {
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top: 3px solid #ffffff;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 滑动删除效果 */
.swipe-delete {
    position: relative;
    overflow: hidden;
}

.swipe-delete::after {
    content: '';
    position: absolute;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, #ef4444);
    transition: right 0.3s ease;
}

.swipe-delete.swiping::after {
    right: 0;
}

/* 响应式调整 */
@media (max-width: 393px) {
    .iphone-container {
        width: 100vw;
        height: 100vh;
        border: none;
        border-radius: 0;
        margin: 0;
    }
}

/* 扑克牌样式 */
.playing-card {
    width: 60px;
    height: 84px;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    padding: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.playing-card:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.playing-card.selected {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px #3b82f6, 0 4px 12px rgba(59, 130, 246, 0.3);
}

.playing-card .card-value {
    font-size: 12px;
    font-weight: bold;
    line-height: 1;
}

.playing-card .card-suit {
    font-size: 20px;
    line-height: 1;
}

.playing-card .card-suit-small {
    font-size: 8px;
    position: absolute;
    top: 2px;
    left: 3px;
}

/* 扑克牌旋转动画 */
.card-rotate {
    animation: cardRotate 3s linear infinite;
}

@keyframes cardRotate {
    0% { transform: rotateY(0deg); }
    50% { transform: rotateY(180deg); }
    100% { transform: rotateY(360deg); }
}

/* 扑克牌选择器网格 */
.card-selector-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
    padding: 16px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

/* 小型扑克牌图标 */
.mini-card {
    width: 24px;
    height: 32px;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    font-weight: bold;
}

/* 扑克牌背景装饰 */
.card-pattern-bg {
    background-image:
        repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(59, 130, 246, 0.05) 10px, rgba(59, 130, 246, 0.05) 20px),
        repeating-linear-gradient(-45deg, transparent, transparent 10px, rgba(220, 38, 38, 0.05) 10px, rgba(220, 38, 38, 0.05) 20px);
}

/* 扑克桌面效果 */
.poker-table {
    background: radial-gradient(ellipse at center, #16a34a 0%, #15803d 100%);
    position: relative;
}

.poker-table::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    border: 3px solid #fbbf24;
    border-radius: 50%;
    opacity: 0.3;
}

/* 筹码堆叠动画 */
.chip-stack-animated {
    animation: chipBounce 2s ease-in-out infinite;
}

@keyframes chipBounce {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-3px); }
}

/* 扑克牌洗牌动画 */
.shuffle-animation {
    animation: shuffle 1.5s ease-in-out infinite;
}

@keyframes shuffle {
    0%, 100% { transform: translateX(0px) rotate(0deg); }
    25% { transform: translateX(-5px) rotate(-2deg); }
    50% { transform: translateX(0px) rotate(0deg); }
    75% { transform: translateX(5px) rotate(2deg); }
}

/* 滑入动画 */
.animate-slide-up {
    animation: slideUp 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes slideUp {
    from {
        transform: translateY(100%) scale(0.9);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

/* 删除确认对话框特殊动画 */
.delete-confirm-enter {
    animation: deleteConfirmEnter 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes deleteConfirmEnter {
    0% {
        transform: translateY(50px) scale(0.8);
        opacity: 0;
    }
    50% {
        transform: translateY(-10px) scale(1.05);
        opacity: 0.8;
    }
    100% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

/* 删除按钮悬浮效果 */
.delete-btn-hover {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.delete-btn-hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.delete-btn-hover:hover::before {
    left: 100%;
}

.delete-btn-hover:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

/* 警告图标脉冲动画 */
.warning-pulse {
    animation: warningPulse 2s ease-in-out infinite;
}

@keyframes warningPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

/* 统计数字动画 */
.stat-number {
    animation: statNumberPop 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes statNumberPop {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 备忘录预览项动画 */
.memo-preview-item {
    animation: memoPreviewSlide 0.4s ease-out;
    animation-fill-mode: both;
}

.memo-preview-item:nth-child(1) { animation-delay: 0.1s; }
.memo-preview-item:nth-child(2) { animation-delay: 0.2s; }
.memo-preview-item:nth-child(3) { animation-delay: 0.3s; }

@keyframes memoPreviewSlide {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 背景过渡动画 */
.app-container {
    transition: background 0.8s ease-in-out;
}

/* 扑克牌闪烁效果 */
.poker-card-shimmer {
    position: relative;
    overflow: hidden;
}

.poker-card-shimmer::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 扑克花色旋转动画 */
.suit-rotate {
    animation: suitRotate 4s linear infinite;
}

@keyframes suitRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 筹码堆叠阴影效果 */
.chip-shadow {
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.1),
        0 4px 8px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.1);
}

/* 扑克桌面光泽效果 */
.table-gloss {
    background:
        radial-gradient(ellipse at 50% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(ellipse at 50% 70%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
}

/* 搜索相关样式 */
.search-highlight {
    background: linear-gradient(120deg, #fbbf24 0%, #f59e0b 100%);
    color: #1f2937;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 600;
}

/* 搜索输入框焦点效果 */
.search-input-focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    border-color: #3b82f6;
}

/* 筛选按钮激活状态 */
.filter-active {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    transform: translateY(-1px);
}

/* 搜索结果动画 */
.search-result-item {
    animation: searchResultSlide 0.3s ease-out;
    animation-fill-mode: both;
}

.search-result-item:nth-child(1) { animation-delay: 0.05s; }
.search-result-item:nth-child(2) { animation-delay: 0.1s; }
.search-result-item:nth-child(3) { animation-delay: 0.15s; }
.search-result-item:nth-child(4) { animation-delay: 0.2s; }
.search-result-item:nth-child(5) { animation-delay: 0.25s; }

@keyframes searchResultSlide {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 无结果状态动画 */
.no-results-bounce {
    animation: noResultsBounce 2s ease-in-out infinite;
}

@keyframes noResultsBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* 搜索计数器动画 */
.results-counter {
    animation: counterPop 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes counterPop {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 删除模式样式 */
.delete-mode {
    background-color: rgba(239, 68, 68, 0.1);
    border-color: #ef4444;
}

/* 选中状态 */
.memo-selected {
    background-color: rgba(239, 68, 68, 0.05);
    border-color: #ef4444;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

/* 批量操作栏 */
.batch-actions-bar {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 确认对话框样式 */
.confirm-dialog {
    backdrop-filter: blur(5px);
}

/* 图标按钮悬浮效果 */
.icon-btn-hover {
    transition: all 0.2s ease;
}

.icon-btn-hover:hover {
    transform: scale(1.1);
    background-color: rgba(255, 255, 255, 0.1);
}

/* 复选框样式 */
input[type="checkbox"] {
    accent-color: #ef4444;
}

/* 禁用状态 */
.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .poker-card {
        background: linear-gradient(145deg, #374151 0%, #4b5563 100%);
        border-color: #6b7280;
        color: #f9fafb;
    }

    .card-suit-black {
        color: #e5e7eb;
    }

    .playing-card {
        background: linear-gradient(145deg, #374151 0%, #4b5563 100%);
        border-color: #6b7280;
        color: #f9fafb;
    }

    .batch-actions-bar {
        background: rgba(55, 65, 81, 0.95);
        color: #f9fafb;
    }
}
